import os
import sys
import queue
import threading
import logging
import webbrowser
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3
import re
import time
import concurrent.futures

from football_analysis_system.config import (
    APP_TITLE, APP_VERSION, COLOR_BG, COLOR_PRIMARY, COLOR_TEXT, TARGET_COMPANIES,
    DATA_URL_TEMPLATE, MATCH_HEADERS, ODDS_URL_TEMPLATE,
    ODDS_HEADERS_TEMPLATE, DB_MATCHES, DATA_DIR,
    DB_WILLIAM_HILL_TEAM, DB_STANDARD_ODDS, DB_STRENGTH_MATCHUP,
    DB_GUANGYISHILI
)

from football_analysis_system.models.match import Match
from football_analysis_system.models.team import Team
from football_analysis_system.models.odds import Odds

from football_analysis_system.db.match_db import MatchDatabase
from football_analysis_system.db.team_db import TeamDatabase

from football_analysis_system.analysis.odds_analyzer import <PERSON>s<PERSON><PERSON><PERSON>zer
from football_analysis_system.analysis.interval_analyzer import <PERSON>val<PERSON><PERSON><PERSON><PERSON>

from football_analysis_system.scrapers.match_scraper import Match<PERSON><PERSON>raper
from football_analysis_system.scrapers.odds_scraper import <PERSON>sScraper

from football_analysis_system.utils.logger import setup_logger, TextWidgetHandler

from .styles import StyleManager
from .widgets.title_bar import TitleBar
from .widgets.status_bar import StatusBar
from .tabs.basic_info_tab import BasicInfoTab
from .tabs.odds_analysis_tab import OddsAnalysisTab
from .tabs.interval_analysis_tab import IntervalAnalysisTab
from .tabs.scraping_tab import ScrapingTab
from .tabs.poisson_tab import PoissonTab
from .tabs.advanced_poisson_tab import AdvancedPoissonTab
from .tabs.fundamental_analysis_tab import FundamentalAnalysisTab
from .tabs.data_management_tab import DataManagementTab

from .tabs.odds_rules_tab import OddsRulesTab
from .tabs.confidence_rules_tab import ConfidenceRulesTab
from .tabs.mahui_league_tab import MahuiLeagueTab

class FootballAnalysisApp:
    """足球比赛分析系统主应用类"""

    def __init__(self, root):
        """
        初始化应用

        Args:
            root: Tkinter根窗口
        """
        self.root = root
        self.root.title(APP_TITLE)
        self.root.geometry("1400x900")
        self.root.configure(bg=COLOR_BG)

        # 数据存储
        self.matches = []
        self.leagues = []  # 存储所有联赛名称
        self.filtered_matches = []  # 存储过滤后的比赛

        # 用户选择
        self.selected_league = tk.StringVar()
        self.selected_match_index = None
        self.selected_company_id = tk.StringVar(value="115")  # 默认使用威廉希尔

        # 爬虫状态
        self.scraping_active = False
        self.message_queue = queue.Queue()

        # 初始化日志
        self.setup_logging()

        # 初始化数据库管理器
        self.match_db = MatchDatabase(DB_MATCHES)

        # 检查广义实力数据库是否存在
        if os.path.exists(DB_GUANGYISHILI):
            logging.info(f"广义实力数据库文件存在: {DB_GUANGYISHILI}")
        else:
            logging.warning(f"广义实力数据库文件不存在: {DB_GUANGYISHILI}")
            # 如果文件不存在，创建一个样例数据库
            logging.info("尝试创建示例广义实力数据库...")
            if self.create_sample_guangyishili_db(DB_GUANGYISHILI):
                logging.info("示例广义实力数据库创建成功")
            else:
                # 不再使用硬编码的相对路径，而是通过config中的DATA_DIR查找
                logging.warning("示例数据库创建失败，检查数据目录...")
                if not os.path.exists(DATA_DIR):
                    logging.error(f"数据目录不存在: {DATA_DIR}")
                    os.makedirs(DATA_DIR, exist_ok=True)
                    logging.info(f"已创建数据目录: {DATA_DIR}")

        # 初始化球队数据库 - 始终使用配置的路径
        self.team_db = TeamDatabase(DB_GUANGYISHILI, self.selected_company_id.get())

        # 初始化分析器
        self.odds_analyzer = OddsAnalyzer(DB_STANDARD_ODDS)
        self.interval_analyzer = IntervalAnalyzer(DB_STRENGTH_MATCHUP)

        # 使用准确的数据库路径
        db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'data', 'football_probabilities_new.db')
        if not os.path.exists(db_path):
            logging.warning(f"胜平负概率数据库文件不存在: {db_path}")
            db_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'data', 'probabilities.db')
            if not os.path.exists(db_path):
                logging.warning(f"在父目录也未找到胜平负概率数据库文件: {db_path}")
                # 使用备选路径
                db_path = 'football_probabilities_new.db'

        logging.info(f"胜平负概率数据库路径: {db_path}")

        # 初始化爬虫
        self.match_scraper = MatchScraper(DATA_URL_TEMPLATE, MATCH_HEADERS)

        # 使用配置的TARGET_COMPANIES确保包含香港马会
        logging.info(f"使用以下目标博彩公司进行爬取: {TARGET_COMPANIES}")
        self.odds_scraper = OddsScraper(ODDS_URL_TEMPLATE, ODDS_HEADERS_TEMPLATE, TARGET_COMPANIES)

        # 加载样式
        self.style = StyleManager.setup_styles()

        # 创建界面
        self.create_widgets()

        # 加载数据
        self.load_all_data()

        # 设置窗口事件处理
        self.setup_event_handlers()

    def setup_logging(self):
        """设置日志记录器"""
        # 从顶级包导入logger模块
        from football_analysis_system.utils.logger import setup_logger, TextWidgetHandler

        # 创建消息队列
        self.message_queue = queue.Queue()

        # 创建日志记录器，提供消息队列和UI文本控件
        setup_logger(
            name="football_analysis",
            log_level=logging.INFO,
            log_file="football_analysis.log",
            message_queue=self.message_queue,
            text_widget=None  # 不直接更新文本控件，通过队列处理
        )

        # 替换根日志处理器，确保所有日志都能显示在UI中
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 添加一个专门处理UI消息的处理器到根日志器
        ui_handler = TextWidgetHandler(self.message_queue)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        ui_handler.setFormatter(formatter)
        root_logger.addHandler(ui_handler)
        root_logger.setLevel(logging.INFO)

        logging.info("日志系统初始化完成")

    def create_widgets(self):
        """创建UI控件"""
        # 主容器
        self.main_container = tk.Frame(self.root, bg=COLOR_BG)
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 标题栏
        self.title_bar = TitleBar(
            self.main_container,
            on_scrape=self.on_scrape_button_click,
            on_cancel=self.on_cancel_scrape_click
        )
        self.title_bar.pack(fill=tk.X, pady=(0, 10))

        # 主体内容区域：分为左右两部分
        self.content_pane = tk.PanedWindow(
            self.main_container,
            orient=tk.HORIZONTAL,
            sashwidth=4,
            sashrelief=tk.RAISED,
            bg=COLOR_BG
        )
        self.content_pane.pack(fill=tk.BOTH, expand=True, pady=10)

        # 左侧：联赛选择和比赛列表
        self.create_left_panel()

        # 右侧：标签页
        self.create_notebook()

        # 状态栏
        self.status_bar = StatusBar(self.main_container)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)

    def create_left_panel(self):
        """创建左侧面板"""
        left_frame = tk.Frame(self.content_pane, bg=COLOR_BG, width=400)
        self.content_pane.add(left_frame, width=400)

        # 联赛选择区
        league_frame = tk.LabelFrame(left_frame, text="联赛选择",
                                   font=("Helvetica", 14, "bold"),
                                   bg=COLOR_BG, fg=COLOR_TEXT,
                                   padx=10, pady=5)
        league_frame.pack(fill=tk.X, padx=5, pady=5)

        # 联赛下拉框
        league_label = tk.Label(league_frame, text="选择联赛:",
                              bg=COLOR_BG, fg=COLOR_TEXT,
                              font=("Helvetica", 10))
        league_label.grid(row=0, column=0, padx=5, pady=5, sticky=tk.W)

        self.league_combobox = ttk.Combobox(league_frame,
                                          textvariable=self.selected_league,
                                          width=30,
                                          state="readonly",
                                          font=("Helvetica", 10))
        self.league_combobox['values'] = self.leagues
        if self.leagues:
            self.league_combobox.current(0)  # 默认选择第一个联赛
        self.league_combobox.grid(row=0, column=1, padx=5, pady=5, sticky=tk.W)
        self.league_combobox.bind("<<ComboboxSelected>>", self.on_league_selected)

        # 搜索按钮
        search_button = ttk.Button(league_frame, text="筛选",
                                 style='Primary.TButton',
                                 command=lambda: self.filter_league_by_button_click())
        search_button.grid(row=0, column=2, padx=5, pady=5)

        # 添加筛选状态提示标签
        self.filter_status_label = tk.Label(league_frame,
                                         text="",
                                         bg=COLOR_BG,
                                         fg=COLOR_TEXT,
                                         font=("Helvetica", 8, "italic"))
        self.filter_status_label.grid(row=2, column=0, columnspan=3, padx=5, pady=2, sticky=tk.W)

        # 添加公司选择下拉框
        company_label = tk.Label(league_frame, text="博彩公司:",
                               bg=COLOR_BG, fg=COLOR_TEXT,
                               font=("Helvetica", 10))
        company_label.grid(row=1, column=0, padx=5, pady=5, sticky=tk.W)

        self.company_combobox = ttk.Combobox(league_frame,
                                           textvariable=self.selected_company_id,
                                           width=30,
                                           state="readonly",
                                           font=("Helvetica", 10))

        # 设置公司列表，在数据加载后更新
        self.company_combobox.grid(row=1, column=1, padx=5, pady=5, sticky=tk.W)
        self.company_combobox.bind("<<ComboboxSelected>>", self.on_company_selected)

        # 切换公司按钮
        switch_button = ttk.Button(league_frame, text="切换",
                                 style='Primary.TButton',
                                 command=self.on_company_selected)
        switch_button.grid(row=1, column=2, padx=5, pady=5)

        # 比赛列表区
        match_frame = tk.LabelFrame(left_frame, text="比赛列表",
                                  font=("Helvetica", 14, "bold"),
                                  bg=COLOR_BG, fg=COLOR_TEXT,
                                  padx=10, pady=5)
        match_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 使用Treeview显示比赛列表
        columns = ("league", "match", "time")
        self.match_tree = ttk.Treeview(match_frame, columns=columns, show="headings", height=20)

        # 定义列
        self.match_tree.heading("league", text="联赛")
        self.match_tree.heading("match", text="比赛")
        self.match_tree.heading("time", text="时间")

        self.match_tree.column("league", width=100)
        self.match_tree.column("match", width=180)
        self.match_tree.column("time", width=100)

        # 添加滚动条
        vsb = ttk.Scrollbar(match_frame, orient="vertical", command=self.match_tree.yview)
        hsb = ttk.Scrollbar(match_frame, orient="horizontal", command=self.match_tree.xview)
        self.match_tree.configure(yscrollcommand=vsb.set, xscrollcommand=hsb.set)

        # 放置控件
        self.match_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        vsb.pack(side=tk.RIGHT, fill=tk.Y)
        hsb.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定事件
        self.match_tree.bind("<Double-1>", self.on_match_selected)
        self.match_tree.bind("<Return>", self.on_match_selected)

        # 配置TreeView样式
        self.match_tree.tag_configure('has_hkjc', background='#E8F5E8', foreground='#2E7D2E')        # 有马会：浅绿色背景，深绿色文字
        self.match_tree.tag_configure('has_odds_no_hkjc', background='#FFF8E1', foreground='#F57F17') # 有赔率无马会：浅黄色背景，深黄色文字
        self.match_tree.tag_configure('no_odds', background='#FFE8E8', foreground='#B85450')          # 无赔率：浅红色背景，深红色文字

    def create_notebook(self):
        """创建右侧标签页"""
        notebook_frame = tk.Frame(self.content_pane, bg=COLOR_BG)
        self.content_pane.add(notebook_frame, width=900, minsize=750)

        # 创建笔记本控件
        self.notebook = ttk.Notebook(notebook_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # 定义标签页索引常量，避免硬编码
        self.TAB_BASIC = 0
        self.TAB_ODDS = 1
        self.TAB_FUNDAMENTAL = 2
        self.TAB_INTERVAL = 3
        self.TAB_POISSON = 4  # 高级泊松分析标签页
        self.TAB_DATA_MGMT = 5
        self.TAB_PREDICTION = 6
        self.TAB_SCRAPING = 7
        self.TAB_ODDS_RULES = 8
        self.TAB_CONFIDENCE_RULES = 9

        # 添加标签页
        self.basic_info_tab = BasicInfoTab(self.notebook)
        self.notebook.add(self.basic_info_tab, text="基本信息")

        self.odds_analysis_tab = OddsAnalysisTab(self.notebook, self.odds_analyzer)
        self.notebook.add(self.odds_analysis_tab, text="赔率分析")

        # 先创建基本面分析标签页以获取基本面分析器
        self.fundamental_analysis_tab = FundamentalAnalysisTab(self.notebook)
        self.notebook.add(self.fundamental_analysis_tab, text="基本面分析")

        # 创建区间分析标签页，传递基本面分析器
        self.interval_analysis_tab = IntervalAnalysisTab(
            self.notebook,
            self.odds_analyzer,
            self.interval_analyzer,
            self.fundamental_analysis_tab  # 传递整个fundamental_analysis_tab对象，而不是只传递analyzer
        )
        self.notebook.add(self.interval_analysis_tab, text="区间分析")

        # 使用新的高级泊松分析标签页
        self.advanced_poisson_tab = AdvancedPoissonTab(self.notebook)
        self.notebook.add(self.advanced_poisson_tab, text="高级泊松分析")

        self.data_mgmt_tab = DataManagementTab(self.notebook, self.message_queue)
        self.notebook.add(self.data_mgmt_tab, text="数据管理")

        # 添加马会联赛查询标签页
        self.mahui_league_tab = MahuiLeagueTab(self.notebook)
        self.notebook.add(self.mahui_league_tab, text="马会联赛")

        # 添加马会联赛查询标签页
        self.mahui_league_tab = MahuiLeagueTab(self.notebook)
        self.notebook.add(self.mahui_league_tab, text="马会联赛")



        # 创建日志显示区域
        self.scraping_tab = ScrapingTab(self.notebook, self.message_queue)
        self.notebook.add(self.scraping_tab, text="数据更新")

        # 添加赔率分析法则标签页
        self.odds_rules_tab = OddsRulesTab(self.notebook)
        self.notebook.add(self.odds_rules_tab, text="赔率法则")

        # 添加信心等级规则标签页
        self.confidence_rules_tab = ConfidenceRulesTab(self.notebook)
        self.notebook.add(self.confidence_rules_tab, text="信心规则")

        # 绑定标签切换事件
        self.notebook.bind("<<NotebookTabChanged>>", self.on_tab_changed)

    def setup_event_handlers(self):
        """设置事件处理"""
        # 窗口调整大小事件
        self.root.bind("<Configure>", self.on_window_resize)

        # 关闭窗口事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 启动消息处理
        self.process_messages()

    def on_window_resize(self, event=None):
        """
        处理窗口大小变化事件
        """
        # 只处理主窗口的大小变化事件
        if event and event.widget == self.root:
            try:
                # 更新内容区域的大小分配比例
                window_width = self.root.winfo_width()

                # 屏蔽可能导致错误的paneconfig/paneconfigure调用
                # 让Tkinter自动处理窗口大小调整

                # 记录日志但不执行可能出错的操作
                logging.info(f"窗口调整为宽度: {window_width}")
            except Exception as e:
                logging.error(f"调整窗口大小时出错: {e}")
                # 不再尝试执行可能出错的操作

    def load_all_data(self):
        """加载所有数据"""
        logging.info("正在加载所有数据...")

        # 加载球队数据
        logging.info("正在加载球队数据...")
        self.team_db.load_team_data()

        # 更新公司下拉框
        self.update_company_combobox()

        # 加载比赛数据
        logging.info("正在加载比赛数据...")
        self.load_matches()

        # 提取联赛列表（这里不会触发筛选）
        self.extract_leagues()

        # 更新联赛下拉框
        self.league_combobox['values'] = self.leagues
        if self.leagues:
            self.league_combobox.current(0)

        # 直接更新比赛列表，而不是重复筛选
        logging.info("正在更新比赛树视图...")
        self.update_match_treeview()

        logging.info("数据加载完成")

    def load_matches(self):
        """加载比赛数据"""
        # 清空现有数据
        self.matches = []
        self.filtered_matches = []

        # 从数据库加载比赛数据
        self.matches = self.match_db.load_matches_and_odds()

        # 初始显示所有比赛，无需筛选
        self.filtered_matches = self.matches.copy()  # 使用copy()避免引用相同的列表

        # 添加调试信息
        unique_leagues = set()
        for match in self.matches:
            if match.league_name:
                unique_leagues.add(match.league_name)

        logging.info(f"加载了 {len(self.matches)} 场比赛")
        logging.info(f"包含 {len(unique_leagues)} 个联赛")
        logging.info(f"联赛列表: {', '.join(unique_leagues)}")

    def get_poisson_database_leagues(self):
        """
        获取泊松分析数据库(football.db)中存在的联赛列表

        Returns:
            list: 联赛名称列表
        """
        try:
            # 获取泊松分析数据库路径
            db_path = os.path.join(DATA_DIR, 'football.db')
            if not os.path.exists(db_path):
                logging.warning(f"泊松分析数据库文件不存在: {db_path}")
                return []

            # 连接数据库
            conn = sqlite3.connect(db_path)

            # 使用poisson_module中的方法获取联赛列表
            from football_analysis_system.analysis.poisson_module import get_available_leagues
            leagues = get_available_leagues(conn)

            # 关闭连接
            conn.close()

            logging.info(f"从泊松分析数据库获取了 {len(leagues)} 个联赛")
            return leagues
        except Exception as e:
            logging.error(f"获取泊松分析数据库联赛列表出错: {e}")
            return []

    def extract_leagues(self):
        """提取联赛名称"""
        if not self.matches:
            self.leagues = ["全部联赛"]
            return

        # 提取爬虫获取的所有联赛名称并去重
        all_leagues = set(match.league_name for match in self.matches if match.league_name)

        # 不再获取泊松数据库的联赛列表进行筛选

        # 显示所有爬取到的联赛
        filtered_leagues = list(all_leagues)

        # 不再记录支持泊松分析的联赛信息

        # 按字母顺序排序联赛名称
        self.leagues = sorted(filtered_leagues)

        # 添加"全部联赛"选项
        self.leagues.insert(0, "全部联赛")

        # 更新联赛下拉框
        self.league_combobox['values'] = self.leagues

        # 确保当前选择正确设置，但不要调用filter_matches_by_league来避免重复加载
        if self.leagues:
            self.league_combobox.current(0)  # 设置为"全部联赛"
            # 确保selected_league变量与下拉框值同步
            self.selected_league.set(self.leagues[0])
            logging.info(f"联赛下拉框初始化为: {self.leagues[0]}")
            # 不再在这里调用filter_matches_by_league，避免重复加载

        logging.info(f"提取了 {len(self.leagues)-1} 个联赛名称")

    def update_match_treeview(self):
        """更新比赛列表"""
        # 清空列表
        try:
            # 使用更可靠的方式清空树视图
            children = self.match_tree.get_children()
            if children:
                self.match_tree.delete(*children)  # 使用*解包一次性删除所有子项
            logging.info("已清空比赛树视图")
        except Exception as e:
            # 不记录删除项目不存在的错误
            if "not found" not in str(e):
                logging.error(f"清空树视图时出错: {e}")

        # 确保filtered_matches不为None
        if not self.filtered_matches:
            logging.warning("没有比赛数据可显示")
            self.status_bar.set_status("没有比赛数据")
            return

        # 添加跨天时间排序逻辑
        def time_sort_key(match_with_index):
            """
            跨天时间排序键函数
            以10点为界：10:00-23:59为当天，00:00-09:59为跨天
            """
            match = match_with_index[0]  # 获取match对象
            try:
                time_str = match.start_time or '00:00'

                # 提取小时和分钟部分进行更精确排序
                hour, minute = 0, 0
                if ':' in time_str:
                    parts = time_str.split(':')
                    hour = int(parts[0])
                    minute = int(parts[1]) if len(parts) > 1 else 0
                else:
                    # 如果时间格式不包含冒号，尝试提取前两位作为小时
                    if len(time_str) >= 2:
                        hour = int(time_str[:2])
                    if len(time_str) >= 4:
                        minute = int(time_str[2:4])

                # 转换为排序权重（包含分钟，以便精确排序）
                if hour >= 10:  # 当天比赛 (10:00-23:59)
                    return hour * 60 + minute
                else:  # 跨天比赛 (00:00-09:59)
                    return (hour + 24) * 60 + minute  # 加24小时排在当天之后

            except (ValueError, TypeError) as e:
                logging.warning(f"时间解析失败: {time_str}, 错误: {e}")
                # 如果时间解析失败，默认排在最后
                return 99 * 60

        # 创建带原始索引的元组列表，以便排序后仍能追踪原始位置
        matches_with_indices = [(match, i) for i, match in enumerate(self.filtered_matches)]

        # 对matches按跨天时间规则排序
        sorted_matches_with_indices = sorted(matches_with_indices, key=time_sort_key)
        logging.info(f"按跨天时间规则排序了 {len(sorted_matches_with_indices)} 场比赛")

        # 添加比赛数据到树视图
        for display_index, (match, original_index) in enumerate(sorted_matches_with_indices):
            home_team = match.home_team or '未知主队'
            away_team = match.away_team or '未知客队'
            league = match.league_name or '未知联赛'
            time = match.start_time or '未知时间'

            # 检查赔率数据状态
            has_odds = bool(match.odds) if hasattr(match, 'odds') and match.odds else False
            has_hkjc = False  # 是否有香港马会赔率

            if has_odds:
                # 检查是否有香港马会赔率
                for company_id, odds_data in match.odds.items():
                    # 香港马会的company_id通常是'432'或包含'香港马会'
                    if company_id == '432' or '香港马会' in str(company_id):
                        has_hkjc = True
                        break

            # 根据赔率状态设置图标和标签
            if not has_odds:
                # 无赔率数据：红色❌
                odds_indicator = " ❌"
                odds_status = 'no_odds'
                status_text = "无赔率"
            elif has_hkjc:
                # 有香港马会赔率：绿色📊
                odds_indicator = " 📊"
                odds_status = 'has_hkjc'
                status_text = "含马会"
            else:
                # 有赔率但无香港马会：黄色⚠️
                odds_indicator = " ⚠️"
                odds_status = 'has_odds_no_hkjc'
                status_text = "无马会"

            match_text = f"{home_team} vs {away_team}{odds_indicator}"

            # 使用原始索引作为tag，这样点击时能正确找到对应的比赛
            # 根据赔率状态设置不同的标签
            tags = [str(original_index), odds_status]

            self.match_tree.insert("", tk.END, values=(league, match_text, time), tags=tuple(tags))

        # 更新状态栏
        total_matches = len(sorted_matches_with_indices)

        # 统计各种状态的比赛数量
        hkjc_count = 0
        odds_no_hkjc_count = 0
        no_odds_count = 0

        for match, _ in sorted_matches_with_indices:
            has_odds = bool(match.odds) if hasattr(match, 'odds') and match.odds else False
            if not has_odds:
                no_odds_count += 1
            else:
                has_hkjc = False
                for company_id, odds_data in match.odds.items():
                    if company_id == '432' or '香港马会' in str(company_id):
                        has_hkjc = True
                        break

                if has_hkjc:
                    hkjc_count += 1
                else:
                    odds_no_hkjc_count += 1

        status_message = f"显示 {total_matches} 场比赛 | 📊有马会:{hkjc_count} ⚠️无马会:{odds_no_hkjc_count} ❌无赔率:{no_odds_count} | 按时间排序"
        self.status_bar.set_status(status_message)

        logging.info(f"显示了 {total_matches} 场比赛：📊含马会 {hkjc_count} 场，⚠️无马会 {odds_no_hkjc_count} 场，❌无赔率 {no_odds_count} 场")

    def filter_matches_by_league(self, league_name):
        """
        按联赛筛选比赛

        Args:
            league_name: 联赛名称
        """
        logging.info(f"执行联赛筛选: {league_name}")

        if not self.matches:
            logging.warning("没有比赛数据可筛选")
            self.filtered_matches = []
            self.update_match_treeview()
            status_text = "错误: 没有比赛数据"
            self.filter_status_label.config(text=status_text)
            return

        if league_name == "全部联赛":
            # 显示所有比赛，不再过滤
            self.filtered_matches = self.matches
            logging.info(f"显示所有比赛，共 {len(self.filtered_matches)} 场")
            status_text = f"显示全部联赛: {len(self.filtered_matches)} 场比赛"
        else:
            # 筛选特定联赛的比赛
            # 添加更多调试信息
            all_leagues = set(match.league_name for match in self.matches if match.league_name)
            logging.info(f"可用联赛: {all_leagues}")

            # 确保大小写和空格处理一致
            normalized_league_name = league_name.strip()
            self.filtered_matches = [match for match in self.matches
                                    if match.league_name and match.league_name.strip() == normalized_league_name]

            match_count = len(self.filtered_matches)
            logging.info(f"按联赛'{league_name}'筛选，找到 {match_count} 场比赛")

            # 如果没有找到比赛，记录更详细的信息
            if match_count == 0:
                status_text = f"未找到 '{league_name}' 联赛的比赛"
                logging.warning(f"联赛筛选失败: 未找到'{league_name}'的比赛")
                for i, match in enumerate(self.matches[:5]):  # 只记录前5场比赛作为示例
                    league = match.league_name or "无联赛名"
                    logging.info(f"示例比赛 {i+1} 联赛名称: '{league}'")
            else:
                status_text = f"筛选结果: {match_count} 场 '{league_name}' 联赛的比赛"

        # 更新比赛列表
        self.update_match_treeview()
        # 更新筛选状态标签
        self.filter_status_label.config(text=status_text)

    def display_match_details(self, match_data):
        """
        显示比赛详细信息

        Args:
            match_data: 比赛数据对象
        """
        if not match_data:
            return

        try:
            # 更新状态栏
            self.status_bar.set_status(f"已选择: {match_data.home_team} vs {match_data.away_team}")

            # 获取主客队数据
            home_team_obj = self.team_db.get_team_data(match_data.home_team)
            away_team_obj = self.team_db.get_team_data(match_data.away_team)

            home_team_data = home_team_obj.to_dict()
            away_team_data = away_team_obj.to_dict()

            # 计算档距差
            gap_difference = self.team_db.calculate_strength_gap(
                match_data.home_team,
                match_data.away_team
            )

            # 将比赛数据转换为字典，只做一次转换
            match_dict = match_data.to_dict()

            # 添加球队对象到match_dict，用于其他标签页分析
            match_dict['home_team_obj'] = home_team_obj
            match_dict['away_team_obj'] = away_team_obj

            # 更新基本信息标签页 - 直接使用
            self.basic_info_tab.update_match_info(
                match_dict,
                home_team_data,
                away_team_data,
                gap_difference
            )

            # 获取当前选择的标签页索引
            current_tab_index = self.notebook.index(self.notebook.select())

            # 根据用户选择的标签页进行按需加载，避免不必要的计算
            # 只加载当前显示的标签页和基本信息标签页
            tabs_to_update = [current_tab_index]
            if current_tab_index != self.TAB_BASIC:  # 不是基本信息标签页
                tabs_to_update.append(self.TAB_BASIC)

            # 按需更新标签页
            for tab_index in tabs_to_update:
                if tab_index == self.TAB_ODDS:  # 赔率分析标签页
                    self.odds_analysis_tab.update_odds_data(match_dict)
                elif tab_index == self.TAB_INTERVAL:  # 区间分析标签页
                    self.interval_analysis_tab.update_interval_data(match_dict, gap_difference)
                elif tab_index == self.TAB_POISSON:  # 泊松分析标签页
                    self.advanced_poisson_tab.update_match_info(match_dict)
                elif tab_index == self.TAB_FUNDAMENTAL:  # 基本面分析标签页
                    self.fundamental_analysis_tab.update_match_info(match_dict)

            # 自动切换到当前选择的标签页（如果不是基本信息标签页）或基本信息标签页
            if current_tab_index == self.TAB_BASIC:
                self.notebook.select(self.TAB_BASIC)
            else:
                self.notebook.select(current_tab_index)

        except Exception as e:
            logging.error(f"显示比赛详情时出错: {e}")
            messagebox.showerror("显示错误", f"显示比赛详情时出错: {e}")

    def on_league_selected(self, event=None):
        """联赛选择事件处理"""
        # 直接从ComboBox获取当前选择的值，不使用StringVar
        selected_league = self.league_combobox.get()
        logging.info(f"联赛选择事件触发: '{selected_league}'")

        if not selected_league:
            # 尝试从ComboBox的当前索引获取值
            current_index = self.league_combobox.current()
            if current_index >= 0:
                values = self.league_combobox['values']
                if values and len(values) > current_index:
                    selected_league = values[current_index]
                    logging.info(f"从ComboBox索引({current_index})恢复选择: '{selected_league}'")

        if not selected_league:
            logging.warning("未选择联赛")
            self.filter_status_label.config(text="错误: 未选择联赛")
            return

        # 更新状态标签
        self.filter_status_label.config(text=f"正在筛选: '{selected_league}'...")

        # 执行筛选
        self.filter_matches_by_league(selected_league)

    def on_match_selected(self, event=None):
        """比赛选择事件处理"""
        selection = self.match_tree.selection()
        if not selection:
            return

        # 获取选中项的索引标签
        item = self.match_tree.item(selection[0])
        item_tags = item['tags']

        if item_tags and len(item_tags) > 0:
            try:
                index = int(item_tags[0])
                if 0 <= index < len(self.filtered_matches):
                    self.selected_match_index = index
                    match_data = self.filtered_matches[index]
                    self.display_match_details(match_data)
            except (ValueError, IndexError) as e:
                logging.error(f"处理选中项时出错: {e}")

    def on_scrape_button_click(self):
        """爬取按钮点击事件处理"""
        if self.scraping_active:
            logging.warning("已有数据更新任务正在进行中")
            return

        # 切换到数据管理标签页 - 使用常量而非硬编码
        self.notebook.select(self.TAB_DATA_MGMT)

        # 开始爬虫任务
        self.start_scraping()

    def on_cancel_scrape_click(self):
        """取消爬取按钮点击事件处理"""
        if not self.scraping_active:
            return

        logging.warning("正在取消数据更新...")
        # 设置标志，线程会检查此标志并自行终止
        self.scraping_active = False

        # 更新UI状态
        self.title_bar.set_scraping_state(False)
        self.scraping_tab.set_status("已取消")

    def on_closing(self):
        """关闭窗口事件处理"""
        if self.scraping_active:
            if messagebox.askyesno("确认", "数据更新正在进行中，确定要关闭程序吗？"):
                self.scraping_active = False
                self.root.destroy()
        else:
            self.root.destroy()

    def start_scraping(self):
        """开始爬取数据"""
        # 重置进度条
        self.scraping_tab.update_progress(0)

        # 清空日志区域
        self.scraping_tab.clear_log()

        # 更新UI状态
        self.title_bar.set_scraping_state(True)
        self.scraping_tab.set_status("正在运行")

        # 设置爬虫标志
        self.scraping_active = True

        # 创建并启动爬虫线程
        scraper_thread = threading.Thread(target=self.scrape_data)
        scraper_thread.daemon = True  # 设为守护线程，以便主线程退出时结束
        scraper_thread.start()

        # 添加初始日志消息
        logging.info("开始更新数据...")

    def save_scraped_data(self, matches_df, all_odds_data):
        """
        保存爬取的数据并记录每个公司的赔率数据数量

        Args:
            matches_df: 比赛数据DataFrame
            all_odds_data: 赔率数据
        """
        try:
            logging.info("保存比赛和赔率数据...")
            self.match_db.save_matches_and_odds(matches_df.to_dict('records'), all_odds_data)

            # 记录每个公司的赔率数据数量
            company_counts = {}
            for match_id, bookmakers in all_odds_data.items():
                for bookmaker, odds in bookmakers.items():
                    if bookmaker not in company_counts:
                        company_counts[bookmaker] = 0
                    company_counts[bookmaker] += 1

            # 输出统计信息
            logging.info("各博彩公司赔率数据统计:")
            for company, count in company_counts.items():
                logging.info(f"  {company}: {count} 场比赛")

            # 特别检查是否有香港马会数据
            hkjc_count = company_counts.get('香港马会', 0)
            if hkjc_count > 0:
                logging.info(f"成功获取香港马会数据: {hkjc_count} 场比赛")
            else:
                logging.warning("未获取到任何香港马会赔率数据")

            # 保存到JSON
            self.match_db.save_to_json(
                matches_df,
                all_odds_data,
                os.path.join(DATA_DIR, 'matches_and_odds.json')
            )

            return True
        except Exception as e:
            logging.error(f"保存数据时出错: {e}")
            return False

    def scrape_data(self):
        """爬虫主线程，执行数据抓取任务"""
        try:
            # 设置数据库
            logging.info("设置数据库并清除现有数据...")
            self.match_db.setup_database()
            self.match_db.clear_data()

            # 获取比赛数据
            logging.info("获取比赛数据...")
            self.update_progress(10)

            # 爬取比赛数据
            matches_df = self.match_scraper.run()
            if matches_df is None or matches_df.empty:
                logging.warning("过滤后未找到比赛，终止任务")
                self.finish_scraping(False)
                return

            match_count = len(matches_df)
            logging.info(f"找到 {match_count} 场需要处理的比赛")
            self.update_progress(40)

            # 获取每场比赛的赔率数据
            match_ids = matches_df['MatchID'].tolist()

            logging.info(f"开始获取 {len(match_ids)} 场比赛的赔率数据, 目标公司包括: {TARGET_COMPANIES}")
            all_odds_data = self.odds_scraper.get_odds_for_matches(match_ids)

            self.update_progress(80)

            # 保存数据
            if self.scraping_active:
                # 使用新方法保存并记录统计信息
                self.save_scraped_data(matches_df, all_odds_data)

                # 清理临时文件
                logging.info("清理临时文件...")
                self.match_scraper.delete_temp_files()

                self.update_progress(95)

                # 重新加载数据到界面
                logging.info("重新加载数据到界面...")
                self.load_matches()
                self.extract_leagues()

                # 显示所有联赛的比赛，不再根据泊松数据库进行筛选
                self.filter_matches_by_league("全部联赛")

                # 更新加载状态
                self.status_bar.set_load_status(len(self.matches), len(self.leagues)-1)

                self.update_progress(100)
                logging.info("数据更新完成！")
                self.finish_scraping(True)
            else:
                logging.warning("数据更新已取消")
                self.finish_scraping(False)

        except Exception as e:
            logging.error(f"数据更新过程中出错: {e}")
            import traceback
            traceback.print_exc()
            self.finish_scraping(False)

    def finish_scraping(self, success):
        """
        完成爬虫任务，更新UI状态

        Args:
            success: 是否成功
        """
        self.scraping_active = False

        # 在主线程中更新UI
        self.root.after(0, lambda: self.update_ui_after_scraping(success))

    def update_ui_after_scraping(self, success):
        """
        爬虫完成后更新UI（在主线程中调用）

        Args:
            success: 是否成功
        """
        # 恢复按钮状态
        self.title_bar.set_scraping_state(False)

        # 更新状态
        if success:
            self.scraping_tab.set_status("已完成")
        else:
            self.scraping_tab.set_status("已终止")

    def update_progress(self, value):
        """
        更新进度条（通过日志队列在主线程中执行）

        Args:
            value: 进度值（0-100）
        """
        logging.info(f"进度更新:{value}")

    def process_messages(self):
        """处理消息队列中的消息，在主线程中更新UI"""
        try:
            while not self.message_queue.empty():
                message = self.message_queue.get_nowait()

                # 只处理进度相关的特殊消息
                if isinstance(message, tuple) and len(message) == 2 and message[0] == "update_progress":
                    value = message[1]
                    self.scraping_tab.update_progress(value)

                self.message_queue.task_done()
        except Exception as e:
            import traceback
            traceback.print_exc()
            logging.error(f"处理消息队列时出错: {e}")
        finally:
            # 每100毫秒检查一次消息队列
            self.root.after(100, self.process_messages)

    def update_company_combobox(self):
        """更新公司下拉框"""
        # 获取可用的公司
        companies = self.team_db.get_available_companies()
        if not companies:
            self.company_combobox['values'] = ["无可用公司"]
            return

        # 更新下拉框
        company_items = []
        for company_id, company_name in companies.items():
            company_items.append(f"{company_name} ({company_id})")

        self.company_combobox['values'] = company_items

        # 设置当前选择
        current_id, current_name = self.team_db.get_current_company_info()
        if current_id and current_name:
            current_item = f"{current_name} ({current_id})"
            try:
                index = company_items.index(current_item)
                self.company_combobox.current(index)
            except ValueError:
                if company_items:
                    self.company_combobox.current(0)

    def on_company_selected(self, event=None):
        """当选择公司时触发"""
        selection = self.company_combobox.get()
        # 从选择中提取ID：格式为"公司名 (ID)"
        company_id = selection.split("(")[-1].replace(")", "").strip()

        # 设置新公司并重新加载数据
        success = self.team_db.set_preferred_company(company_id)

        if success:
            # 重新加载所有依赖于球队评分的分析
            if self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                self.display_match_details(match_data)

            logging.info(f"已切换到公司: {self.team_db.current_company_name}")
        else:
            messagebox.showerror("错误", f"切换公司失败: {company_id}")

        # 更新公司下拉框状态
        self.update_company_combobox()

    def create_sample_guangyishili_db(self, db_path):
        """
        如果原始广义实力数据库不存在，创建一个示例数据库

        Args:
            db_path: 数据库路径
        """
        try:
            # 检查数据库目录是否存在
            db_dir = os.path.dirname(db_path)
            if not os.path.exists(db_dir):
                logging.info(f"创建数据库目录: {db_dir}")
                os.makedirs(db_dir, exist_ok=True)

            # 创建一个新的数据库连接
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # 创建一个简单的表结构
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS team_power_ratings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_id TEXT NOT NULL,
                company_name TEXT NOT NULL,
                team_name TEXT NOT NULL,
                power_category TEXT,
                power_level REAL,
                league_name TEXT,
                update_time TEXT DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            # 添加一些示例数据
            sample_data = [
                # 公司ID, 公司名称, 球队名称, 实力档次, 实力评分, 联赛名称
                ('281', '威廉希尔', '曼城', 'S', 1.0, '英超'),
                ('281', '威廉希尔', '利物浦', 'S', 0.9, '英超'),
                ('281', '威廉希尔', '阿森纳', 'A', 0.85, '英超'),
                ('281', '威廉希尔', '曼联', 'A', 0.8, '英超'),
                ('281', '威廉希尔', '切尔西', 'A', 0.8, '英超'),
                ('281', '威廉希尔', '热刺', 'A', 0.75, '英超'),
                ('281', '威廉希尔', '纽卡斯尔', 'B', 0.7, '英超'),
                ('281', '威廉希尔', '布莱顿', 'B', 0.65, '英超'),
                ('281', '威廉希尔', '伯恩茅斯', 'C', 0.55, '英超'),
                ('281', '威廉希尔', '富勒姆', 'C', 0.5, '英超'),
                ('281', '威廉希尔', '谢菲联', 'D', 0.4, '英超'),
                ('281', '威廉希尔', '卢顿', 'D', 0.35, '英超'),
                ('115', '立博', '曼城', 'S', 0.95, '英超'),
                ('115', '立博', '利物浦', 'S', 0.9, '英超'),
                ('115', '立博', '阿森纳', 'A', 0.85, '英超'),
                ('115', '立博', '曼联', 'A', 0.8, '英超'),
                ('90', '易胜博', '曼城', 'S', 0.95, '英超'),
                ('90', '易胜博', '利物浦', 'S', 0.9, '英超'),
                ('90', '易胜博', '阿森纳', 'A', 0.85, '英超'),
                ('90', '易胜博', '曼联', 'A', 0.8, '英超'),
                # 香港马会数据
                ('432', '香港马会', '曼城', 'S', 0.98, '英超'),
                ('432', '香港马会', '利物浦', 'S', 0.92, '英超'),
                ('432', '香港马会', '阿森纳', 'A', 0.84, '英超'),
                ('432', '香港马会', '曼联', 'A', 0.78, '英超'),
                ('432', '香港马会', '切尔西', 'A', 0.76, '英超')
            ]

            cursor.executemany('''
            INSERT INTO team_power_ratings
                (company_id, company_name, team_name, power_category, power_level, league_name)
            VALUES (?, ?, ?, ?, ?, ?)
            ''', sample_data)

            # 提交更改
            conn.commit()
            conn.close()

            logging.info(f"已创建示例广义实力数据库: {db_path}")
            logging.info(f"添加了 {len(sample_data)} 条示例数据")

            return True
        except Exception as e:
            logging.error(f"创建示例广义实力数据库失败: {e}")
            import traceback
            logging.error(traceback.format_exc())
            return False

    def on_tab_changed(self, event=None):
        """标签页切换事件处理"""
        try:
            # 获取当前选择的标签页索引
            current_tab_index = self.notebook.index(self.notebook.select())

            # 如果是赔率分析标签页，并且有选中的比赛，则加载数据
            if current_tab_index == self.TAB_ODDS and self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                match_dict = match_data.to_dict()
                # 重新获取主客队数据
                match_dict['home_team_obj'] = self.team_db.get_team_data(match_data.home_team)
                match_dict['away_team_obj'] = self.team_db.get_team_data(match_data.away_team)
                self.odds_analysis_tab.update_odds_data(match_dict)

            # 如果是区间分析标签页，并且有选中的比赛，则加载数据
            elif current_tab_index == self.TAB_INTERVAL and self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                match_dict = match_data.to_dict()
                # 重新获取主客队数据
                match_dict['home_team_obj'] = self.team_db.get_team_data(match_data.home_team)
                match_dict['away_team_obj'] = self.team_db.get_team_data(match_data.away_team)
                # 计算档距差
                gap_difference = self.team_db.calculate_strength_gap(
                    match_data.home_team,
                    match_data.away_team
                )
                self.interval_analysis_tab.update_interval_data(match_dict, gap_difference)

            # 如果是泊松分析标签页，并且有选中的比赛，则加载数据
            elif current_tab_index == self.TAB_POISSON and self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                match_dict = match_data.to_dict()
                self.advanced_poisson_tab.update_match_info(match_dict)

            # 如果是基本面分析标签页，并且有选中的比赛，则加载数据
            elif current_tab_index == self.TAB_FUNDAMENTAL and self.selected_match_index is not None:
                match_data = self.filtered_matches[self.selected_match_index]
                match_dict = match_data.to_dict()
                self.fundamental_analysis_tab.update_match_info(match_dict)

        except Exception as e:
            logging.error(f"标签页切换事件处理出错: {e}")

    def filter_league_by_button_click(self):
        """筛选按钮点击处理，直接从下拉框获取当前值进行筛选"""
        current_index = self.league_combobox.current()
        if current_index >= 0:
            values = self.league_combobox['values']
            if values and len(values) > current_index:
                selected_league = values[current_index]
                logging.info(f"筛选按钮点击: 选择'{selected_league}'进行筛选")
                self.filter_status_label.config(text=f"正在筛选: '{selected_league}'...")
                self.filter_matches_by_league(selected_league)
                return

        # 如果没有有效选择，尝试使用"全部联赛"
        logging.warning("筛选按钮点击: 未找到有效的联赛选择")
        if self.leagues and len(self.leagues) > 0:
            self.filter_matches_by_league(self.leagues[0])
        else:
            self.filter_status_label.config(text="错误: 未找到可用联赛")