import tkinter as tk
from tkinter import ttk, messagebox
import logging
import sqlite3
from datetime import datetime, timedelta
from football_analysis_system.config import COLOR_BG, COLOR_PRIMARY, FONT_SUBHEADER, DB_GUANGYISHILI
from football_analysis_system.core.path_resolver import PathResolver
import os

class MahuiLeagueTab(tk.Frame):
    """马会联赛查询标签页"""

    def __init__(self, parent, db_path=None):
        """
        初始化马会联赛查询标签页

        Args:
            parent: 父容器
            db_path: 数据库路径
        """
        super().__init__(parent, bg=COLOR_BG)

        # 数据库路径
        self.guangyi_db_path = DB_GUANGYISHILI
        self.football_db_path = PathResolver.get_database_path('football.db')
        
        # 记录数据库路径用于调试
        logging.info(f"马会联赛查询标签页使用广义实力数据库: {self.guangyi_db_path}")
        logging.info(f"马会联赛查询标签页使用足球数据库: {self.football_db_path}")
        
        # 检查数据库文件是否存在
        if not os.path.exists(self.guangyi_db_path):
            logging.error(f"广义实力数据库文件不存在: {self.guangyi_db_path}")
        
        if not os.path.exists(self.football_db_path):
            logging.error(f"足球数据库文件不存在: {self.football_db_path}")
            
        self.mahui_leagues = []  # 包含马会的联赛列表
        self.upcoming_matches = []  # 未来7天的比赛
        
        self.create_widgets()
        self.load_mahui_leagues()

    def create_widgets(self):
        """创建标签页控件"""
        # 容器框架
        main_container = ttk.Frame(self)
        main_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # 顶部标题和控制区域
        top_frame = ttk.Frame(main_container)
        top_frame.pack(fill=tk.X, padx=5, pady=5)

        # 标题
        title_label = ttk.Label(top_frame, text="马会联赛查询",
                             font=FONT_SUBHEADER,
                             foreground=COLOR_PRIMARY,
                             background=COLOR_BG,
                             style="Subheader.TLabel")
        title_label.pack(side=tk.LEFT, padx=5)

        # 刷新按钮
        refresh_button = ttk.Button(top_frame, text="刷新", width=8,
                                  command=self.refresh_data,
                                  style="Primary.TButton")
        refresh_button.pack(side=tk.RIGHT, padx=5)

        # 查询按钮
        query_button = ttk.Button(top_frame, text="查询比赛", width=10,
                                command=self.query_upcoming_matches,
                                style="Primary.TButton")
        query_button.pack(side=tk.RIGHT, padx=5)

        # 联赛列表区域
        leagues_frame = ttk.LabelFrame(main_container, text="包含马会的联赛")
        leagues_frame.pack(fill=tk.X, padx=5, pady=5)

        # 创建联赛列表
        self.leagues_listbox = tk.Listbox(leagues_frame, height=6, selectmode=tk.MULTIPLE)
        self.leagues_listbox.pack(fill=tk.X, padx=5, pady=5)

        # 比赛列表区域
        matches_frame = ttk.LabelFrame(main_container, text="未来7天比赛")
        matches_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 创建Treeview用于显示比赛
        columns = ("league", "home_team", "away_team", "match_time")
        self.matches_tree = ttk.Treeview(matches_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        self.matches_tree.heading("league", text="联赛")
        self.matches_tree.heading("home_team", text="主队")
        self.matches_tree.heading("away_team", text="客队")
        self.matches_tree.heading("match_time", text="比赛时间")
        
        # 设置列宽
        self.matches_tree.column("league", width=150)
        self.matches_tree.column("home_team", width=150)
        self.matches_tree.column("away_team", width=150)
        self.matches_tree.column("match_time", width=200)
        
        # 添加滚动条
        matches_scrollbar = ttk.Scrollbar(matches_frame, orient=tk.VERTICAL, command=self.matches_tree.yview)
        self.matches_tree.configure(yscrollcommand=matches_scrollbar.set)
        
        # 布局
        self.matches_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        matches_scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=5)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = ttk.Label(main_container, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, padx=5)

    def load_mahui_leagues(self):
        """从广义实力表中加载包含马会的联赛"""
        try:
            if not os.path.exists(self.guangyi_db_path):
                self.status_var.set("广义实力数据库不存在")
                return

            conn = sqlite3.connect(self.guangyi_db_path)
            cursor = conn.cursor()
            
            # 查询包含马会(香港马会)的联赛
            cursor.execute('''
            SELECT DISTINCT league_name
            FROM team_power_ratings
            WHERE company_name LIKE '%马会%' OR company_name LIKE '%香港马会%'
            ORDER BY league_name
            ''')
            
            leagues = cursor.fetchall()
            self.mahui_leagues = [league[0] for league in leagues if league[0]]
            
            conn.close()
            
            # 更新联赛列表框
            self.leagues_listbox.delete(0, tk.END)
            for league in self.mahui_leagues:
                self.leagues_listbox.insert(tk.END, league)
                
            # 更新状态栏
            self.status_var.set(f"找到 {len(self.mahui_leagues)} 个包含马会的联赛")
            
        except Exception as e:
            logging.error(f"加载马会联赛时出错: {e}")
            messagebox.showerror("错误", f"加载马会联赛失败: {e}")
            self.status_var.set("加载马会联赛失败")

    def query_upcoming_matches(self):
        """查询选中联赛未来7天的比赛"""
        try:
            # 获取选中的联赛
            selected_indices = self.leagues_listbox.curselection()
            if not selected_indices:
                messagebox.showwarning("提示", "请至少选择一个联赛")
                return
                
            selected_leagues = [self.mahui_leagues[i] for i in selected_indices]
            
            if not os.path.exists(self.football_db_path):
                self.status_var.set("足球数据库不存在")
                return

            # 计算未来7天的时间范围
            now = datetime.now()
            future_date = now + timedelta(days=7)
            
            # 格式化时间字符串
            now_str = now.strftime('%Y-%m-%d %H:%M:%S')
            future_str = future_date.strftime('%Y-%m-%d %H:%M:%S')
            
            conn = sqlite3.connect(self.football_db_path)
            cursor = conn.cursor()
            
            # 清空现有比赛列表
            for item in self.matches_tree.get_children():
                self.matches_tree.delete(item)
            
            all_matches = []
            
            # 为每个选中的联赛查询比赛
            for league in selected_leagues:
                cursor.execute('''
                SELECT league_name, home_team_name, away_team_name, match_time
                FROM matches
                WHERE league_name = ? 
                AND match_time >= ? 
                AND match_time <= ?
                AND match_status = 0
                ORDER BY match_time
                ''', (league, now_str, future_str))
                
                matches = cursor.fetchall()
                all_matches.extend(matches)
                
            conn.close()
            
            # 按联赛分组显示比赛
            current_league = None
            for match in sorted(all_matches, key=lambda x: (x[0], x[3])):  # 按联赛和时间排序
                league_name, home_team, away_team, match_time = match
                
                # 如果是新联赛，插入分隔行
                if current_league != league_name:
                    current_league = league_name
                    # 插入联赛标题行
                    self.matches_tree.insert("", tk.END, values=(f"=== {league_name} ===", "", "", ""), tags=("league_header",))
                
                # 插入比赛行
                self.matches_tree.insert("", tk.END, values=(league_name, home_team, away_team, match_time))
            
            # 配置联赛标题行的样式
            self.matches_tree.tag_configure("league_header", background="#e3f2fd", font=("Arial", 10, "bold"))
            
            # 更新状态栏
            self.status_var.set(f"找到 {len(all_matches)} 场未来7天的比赛")
            
        except Exception as e:
            logging.error(f"查询未来比赛时出错: {e}")
            messagebox.showerror("错误", f"查询未来比赛失败: {e}")
            self.status_var.set("查询未来比赛失败")

    def refresh_data(self):
        """刷新数据"""
        self.load_mahui_leagues()
        # 清空比赛列表
        for item in self.matches_tree.get_children():
            self.matches_tree.delete(item)
        self.status_var.set("数据已刷新")
