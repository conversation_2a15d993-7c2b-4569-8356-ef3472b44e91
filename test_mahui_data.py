#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os
from football_analysis_system.config import DB_GUANGYISHILI
from football_analysis_system.core.path_resolver import PathResolver

def test_mahui_data():
    print('广义实力数据库路径:', DB_GUANGYISHILI)
    print('文件是否存在:', os.path.exists(DB_GUANGYISHILI))

    if os.path.exists(DB_GUANGYISHILI):
        conn = sqlite3.connect(DB_GUANGYISHILI)
        cursor = conn.cursor()
        
        # 查看表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print('数据库中的表:', [t[0] for t in tables])
        
        if 'team_power_ratings' in [t[0] for t in tables]:
            # 查看是否有马会数据
            cursor.execute("SELECT DISTINCT company_name FROM team_power_ratings WHERE company_name LIKE '%马会%' OR company_name LIKE '%香港马会%'")
            companies = cursor.fetchall()
            print('包含马会的公司:', [c[0] for c in companies])
            
            # 查看马会的联赛
            cursor.execute("SELECT DISTINCT league_name FROM team_power_ratings WHERE company_name LIKE '%马会%' OR company_name LIKE '%香港马会%'")
            leagues = cursor.fetchall()
            print('马会的联赛数量:', len(leagues))
            if leagues:
                print('前5个联赛:', [l[0] for l in leagues[:5]])
        
        conn.close()

    # 检查football.db
    football_db = PathResolver.get_database_path('football.db')
    print('\nfootball.db路径:', football_db)
    print('文件是否存在:', os.path.exists(football_db))

    if os.path.exists(football_db):
        conn = sqlite3.connect(football_db)
        cursor = conn.cursor()
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print('football.db中的表:', [t[0] for t in tables])
        
        if 'matches' in [t[0] for t in tables]:
            cursor.execute("SELECT COUNT(*) FROM matches")
            count = cursor.fetchone()[0]
            print('比赛总数:', count)
            
            # 查看最近的比赛
            cursor.execute("SELECT league_name, match_time FROM matches ORDER BY match_time DESC LIMIT 5")
            recent = cursor.fetchall()
            print('最近5场比赛:')
            for r in recent:
                print(f'  {r[0]} - {r[1]}')
        
        conn.close()

if __name__ == "__main__":
    test_mahui_data()
