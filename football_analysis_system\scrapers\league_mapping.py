"""
联赛名称映射模块，用于标准化各种不同表达的联赛名称。
按照用户提供的固定联赛列表进行精确匹配，避免错误映射。
"""

# 标准联赛名称映射，仅包含确定的别名
LEAGUE_STANDARD_NAMES = {
    # 英超
    "英超": ["英格兰超级联赛", "英格兰超", "英格兰顶级联赛", "Premier League"],
    # 英冠
    "英冠": ["英格兰冠军联赛", "英格兰冠军", "EFL Championship"],
    # 英甲
    "英甲": ["英格兰甲级联赛", "英格兰甲级", "EFL League One"],
    # 英乙
    "英乙": ["英格兰乙级联赛", "英格兰乙级", "EFL League Two"],
    # 西甲
    "西甲": ["西班牙甲级联赛", "西班牙甲级", "西班牙足球甲级联赛", "La Liga"],
    # 西乙
    "西乙": ["西班牙乙级联赛", "西班牙乙级", "西班牙足球乙级联赛", "La Liga 2"],
    # 意甲
    "意甲": ["意大利甲级联赛", "意大利甲级", "意大利足球甲级联赛", "Serie A"],
    # 意乙
    "意乙": ["意大利乙级联赛", "意大利乙级", "意大利足球乙级联赛", "Serie B"],
    # 德甲
    "德甲": ["德国甲级联赛", "德国甲级", "德国足球甲级联赛", "Bundesliga"],
    # 德乙
    "德乙": ["德国乙级联赛", "德国乙级", "德国足球乙级联赛", "2.Bundesliga"],
    # 法甲
    "法甲": ["法国甲级联赛", "法国甲级", "法国足球甲级联赛", "Ligue 1"],
    # 法乙
    "法乙": ["法国乙级联赛", "法国乙级", "法国足球乙级联赛", "Ligue 2"],
    # 葡超
    "葡超": ["葡萄牙超级联赛", "葡萄牙超", "葡萄牙足球超级联赛", "Primeira Liga"],
    # 葡甲
    "葡甲": ["葡萄牙甲级联赛", "葡萄牙甲级", "葡萄牙足球甲级联赛", "Liga Portugal 2"],
    # 荷甲
    "荷甲": ["荷兰甲级联赛", "荷兰甲级", "荷兰足球甲级联赛", "Eredivisie"],
    # 荷乙
    "荷乙": ["荷兰乙级联赛", "荷兰乙级", "荷兰足球乙级联赛", "Eerste Divisie"],
    # 苏超
    "苏超": ["苏格兰超级联赛", "苏格兰超", "苏格兰足球超级联赛", "Scottish Premiership"],
    # 苏冠
    "苏冠": ["苏格兰冠军联赛", "苏格兰冠军", "苏格兰足球冠军联赛", "Scottish Championship"],
    # 苏甲
    "苏甲": ["苏格兰甲级联赛", "苏格兰甲级", "苏格兰足球甲级联赛", "Scottish League One"],
    # 苏乙
    "苏乙": ["苏格兰乙级联赛", "苏格兰乙级", "苏格兰足球乙级联赛", "Scottish League Two"],
    # 瑞士超
    "瑞士超": ["瑞士超级联赛", "瑞士超", "瑞士足球超级联赛", "Swiss Super League"],
    # 瑞士甲
    "瑞士甲": ["瑞士甲级联赛", "瑞士甲级", "瑞士足球甲级联赛", "Swiss Challenge League"],
    # 瑞典超
    "瑞典超": ["瑞典超级联赛", "瑞典超", "瑞典足球超级联赛", "Allsvenskan"],
    # 瑞甲
    "瑞甲": ["瑞典甲级联赛", "瑞典甲级", "瑞典足球甲级联赛", "瑞典甲", "Superettan"],
    # 挪超
    "挪超": ["挪威超级联赛", "挪威超", "挪威足球超级联赛", "Eliteserien"],
    # 挪甲
    "挪甲": ["挪威甲级联赛", "挪威甲级", "挪威足球甲级联赛", "Norwegian First Division"],
    # 俄超
    "俄超": ["俄罗斯超级联赛", "俄罗斯超", "俄罗斯足球超级联赛", "Russian Premier League"],
    # 俄甲
    "俄甲": ["俄罗斯甲级联赛", "俄罗斯甲级", "俄罗斯足球甲级联赛", "Russian First League"],
    # 白俄超
    "白俄超": ["白俄罗斯超级联赛", "白俄罗斯超", "白俄罗斯足球超级联赛", "Belarusian Premier League"],
    # 土耳其超
    "土耳其超": ["土耳其超级联赛", "土超", "土耳其足球超级联赛", "Turkish Super League", "Süper Lig"],
    # 爱尔兰超
    "爱尔兰超": ["爱尔兰超级联赛", "爱超", "爱尔兰足球超级联赛", "League of Ireland Premier"],
    # 爱尔兰甲
    "爱尔兰甲": ["爱尔兰甲级联赛", "爱甲", "爱尔兰足球甲级联赛", "League of Ireland First"],
    # 阿根廷甲
    "阿根廷甲": ["阿根廷甲级联赛", "阿甲", "阿根廷足球甲级联赛", "Argentine Primera División"],
    # 阿根廷乙
    "阿根廷乙": ["阿根廷乙级联赛", "阿乙", "阿根廷足球乙级联赛", "Primera Nacional"],
    # 智利甲
    "智利甲": ["智利甲级联赛", "智甲", "智利足球甲级联赛", "Chilean Primera División"],
    # 智利乙
    "智利乙": ["智利乙级联赛", "智乙", "智利足球乙级联赛", "Primera B de Chile"],
    # 巴西甲
    "巴西甲": ["巴西甲级联赛", "巴甲", "巴西足球甲级联赛", "Brasileirão Série A"],
    # 巴西乙
    "巴西乙": ["巴西乙级联赛", "巴乙", "巴西足球乙级联赛", "Brasileirão Série B"],
    # 中超
    "中超": ["中国超级联赛", "中国足球超级联赛", "Chinese Super League"],
    # 澳超
    "澳超": ["澳大利亚超级联赛", "澳大利亚超", "澳大利亚足球超级联赛", "A-League Men"],
    # 日职联
    "日职联": ["日本职业足球联赛", "J联赛", "J1联赛", "J1 League"],
    # 日职乙
    "日职乙": ["日本职业足球乙级联赛", "J2联赛", "J2 League"],
    # 日职丙
    "日职丙": ["日本职业足球丙级联赛", "J3联赛", "J3 League"],
    # 日职女甲
    "日职女甲": ["日本女子职业足球甲级联赛", "WE League", "日本女子联赛"],
    # 日职女乙
    "日职女乙": ["日本女子职业足球乙级联赛", "Nadeshiko League", "日本女子甲级联赛"],
    # 韩K
    "韩K": ["韩国K联赛", "韩国K1联赛", "K联赛", "K League 1"],
    # 韩K2
    "韩K2": ["韩国K2联赛", "K2联赛", "K League 2"],
    # 阿联酋超
    "阿联酋超": ["阿联酋超级联赛", "阿联酋超", "阿联酋足球超级联赛", "UAE Pro League"],
    # 阿联酋甲
    "阿联酋甲": ["阿联酋甲级联赛", "阿联酋甲级", "阿联酋足球甲级联赛", "UAE First Division"],
    # 乌兹超
    "乌兹超": ["乌兹别克斯坦超级联赛", "乌兹别克超", "乌兹别克足球超级联赛", "Uzbekistan Super League"],
    # 澳南超
    "澳南超": ["澳洲南部超级联赛", "澳大利亚南部超级联赛", "南澳超级联赛", "FFSA Premier League"],
    # 澳维甲
    "澳维甲": ["澳洲维多利亚甲级联赛", "澳大利亚维多利亚甲级联赛", "维多利亚甲级联赛", "Victoria NPL 2"],
    # 澳维超
    "澳维超": ["澳洲维多利亚超级联赛", "澳大利亚维多利亚超级联赛", "维多利亚超级联赛", "Victoria NPL"],
    # 澳昆超
    "澳昆超": ["澳洲昆士兰超级联赛", "澳大利亚昆士兰超级联赛", "昆士兰超级联赛", "Queensland NPL"],
    # 澳西超
    "澳西超": ["澳洲西澳超级联赛", "澳大利亚西澳超级联赛", "西澳超级联赛", "Western Australia NPL"],
    # 澳布超
    "澳布超": ["澳洲布里斯班超级联赛", "澳大利亚布里斯班超级联赛", "布里斯班超级联赛", "Brisbane NPL"],
    # 澳威超
    "澳威超": ["澳洲新南威尔士超级联赛", "澳大利亚新南威尔士超级联赛", "新南威尔士超级联赛", "NSW NPL"],
    # 美职业
    "美职业": ["美国职业大联盟", "美国职业足球大联盟", "MLS", "Major League Soccer"],
    # 美甲
    "美甲": ["美国足球冠军联赛", "USL Championship", "USLC", "USL冠军联赛"],
    # 印度超
    "印度超": ["印度超级联赛", "印度足球超级联赛", "Indian Super League", "ISL"],
    # 印度甲
    "印度甲": ["印度甲级联赛", "印度足球甲级联赛", "I-League", "印度联赛"],
    # 越南联
    "越南联": ["越南足球超级联赛", "越南超级联赛", "V.League 1", "越南甲级联赛"],
    # 越南甲
    "越南甲": ["越南足球甲级联赛", "越南乙级联赛", "V.League 2"],
    # 马来超
    "马来超": ["马来西亚超级联赛", "马来西亚足球超级联赛", "Malaysia Super League"],
    # 马来甲
    "马来甲": ["马来西亚甲级联赛", "马来西亚足球甲级联赛", "Malaysia Premier League"],
    # 巴林超
    "巴林超": ["巴林超级联赛", "巴林足球超级联赛", "Bahraini Premier League"],
    # 瑞典乙
    "瑞典乙": ["瑞典乙级联赛", "瑞典足球乙级联赛", "Ettan", "Division 1"],
    # 瑞典丙
    "瑞典丙": ["瑞典丙级联赛", "瑞典足球丙级联赛", "Division 2"],
    # 丹麦超
    "丹麦超": ["丹麦超级联赛", "丹麦足球超级联赛", "Danish Superliga"],
    # 丹麦甲
    "丹麦甲": ["丹麦甲级联赛", "丹麦足球甲级联赛", "NordicBet Liga", "Danish 1st Division"],
    # 丹麦乙
    "丹麦乙": ["丹麦乙级联赛", "丹麦足球乙级联赛", "2. Division"],
    # 丹麦丙
    "丹麦丙": ["丹麦丙级联赛", "丹麦足球丙级联赛", "3. Division"],
    # 乌兹甲
    "乌兹甲": ["乌兹别克斯坦甲级联赛", "乌兹别克甲级", "Uzbekistan First League"],
    # 克亚甲
    "克亚甲": ["克罗地亚甲级联赛", "克罗地亚甲级", "Croatian First League", "Prva HNL"],
    # 冰岛甲
    "冰岛甲": ["冰岛甲级联赛", "冰岛甲级", "Iceland First Division"],
    # 冰岛超
    "冰岛超": ["冰岛超级联赛", "冰岛超级", "Iceland Premier League", "Úrvalsdeild"],
    # 匈甲
    "匈甲": ["匈牙利甲级联赛", "匈牙利甲级", "Hungarian OTP Bank Liga"],
    # 南非甲
    "南非甲": ["南非甲级联赛", "南非甲级", "South African First Division", "GladAfrica Championship"],
    # 南非超
    "南非超": ["南非超级联赛", "南非超级", "South African Premier League", "PSL"],
    # 哈萨克甲
    "哈萨克甲": ["哈萨克斯坦甲级联赛", "哈萨克甲级", "Kazakhstan First Division"],
    # 哈萨克超
    "哈萨克超": ["哈萨克斯坦超级联赛", "哈萨克超级", "Kazakhstan Premier League"],
    # 土甲
    "土甲": ["土耳其甲级联赛", "土耳其甲级", "Turkish 1. Lig", "TFF First League"],
    # 墨西哥甲
    "墨西哥甲": ["墨西哥甲级联赛", "墨西哥甲级", "Liga MX", "Mexican Primera División"],
    # 巴拉乙
    "巴拉乙": ["巴拉圭乙级联赛", "巴拉圭乙级", "Paraguayan Segunda División"],
    # 巴拉甲
    "巴拉甲": ["巴拉圭甲级联赛", "巴拉圭甲级", "Paraguayan Primera División"],
    # 巴林甲
    "巴林甲": ["巴林甲级联赛", "巴林甲级", "Bahraini Second Division"],
    # 拉脱甲
    "拉脱甲": ["拉脱维亚甲级联赛", "拉脱维亚甲级", "Latvian First League"],
    # 拉脱超
    "拉脱超": ["拉脱维亚超级联赛", "拉脱维亚超级", "Latvian Higher League"],
    # 摩洛超
    "摩洛超": ["摩洛哥超级联赛", "摩洛哥超级", "Moroccan Botola Pro"],
    # 斯亚甲
    "斯亚甲": ["斯洛文尼亚甲级联赛", "斯洛文尼亚甲级", "Slovenian PrvaLiga"],
    # 格鲁甲
    "格鲁甲": ["格鲁吉亚甲级联赛", "格鲁吉亚甲级", "Georgian Erovnuli Liga"],
    # 沙特联
    "沙特联": ["沙特阿拉伯联赛", "沙特超级联赛", "Saudi Pro League"],
    # 波斯甲
    "波斯甲": ["波斯尼亚甲级联赛", "波黑甲级", "Bosnia Premier League"],
    # 泰甲
    "泰甲": ["泰国甲级联赛", "泰国甲级", "Thai League 2"],
    # 泰超
    "泰超": ["泰国超级联赛", "泰国超级", "Thai League 1"],
    # 爱沙甲
    "爱沙甲": ["爱沙尼亚甲级联赛", "爱沙尼亚甲级", "Estonian Meistriliiga"],

    # 白俄甲
    "白俄甲": ["白俄罗斯甲级联赛", "白俄甲级", "Belarusian First League"],
    # 秘鲁甲
    "秘鲁甲": ["秘鲁甲级联赛", "秘鲁甲级", "Peruvian Primera División"],
    # 立陶乙
    "立陶乙": ["立陶宛乙级联赛", "立陶宛乙级", "Lithuanian Second League"],
    # 立陶甲
    "立陶甲": ["立陶宛甲级联赛", "立陶宛甲级", "Lithuanian A Lyga"],
    # 英乙
    "英乙": ["英格兰乙级联赛", "英格兰乙级", "EFL League Two"],
    # 苏甲
    "苏甲": ["苏格兰甲级联赛", "苏格兰甲级", "Scottish League One"],
    # 苏乙
    "苏乙": ["苏格兰乙级联赛", "苏格兰乙级", "Scottish League Two"],

    # 奥威超
    "奥威超": ["奥地利威尔士超级联赛", "奥地利威尔士联赛", "Austrian WSL"],

    # 芬超
    "芬超": ["芬兰超级联赛", "芬兰足球超级联赛", "Veikkausliiga", "Finnish Veikkausliiga", "芬兰甲级联赛"]
}

# 联赛别名，按语言分类
LEAGUE_ALIASES = {
    "英超": {
        "中文": ["英格兰超级联赛", "英格兰超", "英格兰顶级联赛"],
        "英文": ["Premier League", "EPL", "English Premier League"]
    },
    "英冠": {
        "中文": ["英格兰冠军联赛", "英格兰冠军"],
        "英文": ["EFL Championship", "Championship", "English Championship"]
    },
    "西甲": {
        "中文": ["西班牙甲级联赛", "西班牙甲级", "西班牙足球甲级联赛"],
        "英文": ["La Liga", "Spanish La Liga", "Primera División"]
    },
    "意甲": {
        "中文": ["意大利甲级联赛", "意大利甲级", "意大利足球甲级联赛"],
        "英文": ["Serie A", "Italian Serie A"]
    },
    "德甲": {
        "中文": ["德国甲级联赛", "德国甲级", "德国足球甲级联赛"],
        "英文": ["Bundesliga", "German Bundesliga"]
    },
    "法甲": {
        "中文": ["法国甲级联赛", "法国甲级", "法国足球甲级联赛"],
        "英文": ["Ligue 1", "French Ligue 1"]
    },
    "芬超": {
        "中文": ["芬兰超级联赛", "芬兰足球超级联赛", "芬兰甲级联赛"],
        "英文": ["Veikkausliiga", "Finnish Veikkausliiga", "Finnish Premier League"]
    }
}

# 联赛关键词匹配，更加精确
LEAGUE_KEYWORDS = {
    "英超": ["英格兰超级", "英超联赛"],
    "英冠": ["英格兰冠军", "英冠联赛"],
    "英甲": ["英格兰甲级", "英甲联赛"],
    "英乙": ["英格兰乙级", "英乙联赛"],
    "西甲": ["西班牙甲级", "西甲联赛"],
    "西乙": ["西班牙乙级", "西乙联赛"],
    "意甲": ["意大利甲级", "意甲联赛"],
    "意乙": ["意大利乙级", "意乙联赛"],
    "德甲": ["德国甲级", "德甲联赛"],
    "德乙": ["德国乙级", "德乙联赛"],
    "法甲": ["法国甲级", "法甲联赛"],
    "法乙": ["法国乙级", "法乙联赛"]
}

# 更严格的模糊匹配模式
LEAGUE_FUZZY_PATTERNS = {
    "英超": r"^(英超|英格兰超级联赛|Premier\s*League)$",
    "英冠": r"^(英冠|英格兰冠军联赛|Championship)$",
    "英甲": r"^(英甲|英格兰甲级联赛|League\s*One)$",
    "英乙": r"^(英乙|英格兰乙级联赛|League\s*Two)$",
    "西甲": r"^(西甲|西班牙甲级联赛|La\s*Liga)$",
    "西乙": r"^(西乙|西班牙乙级联赛|La\s*Liga\s*2)$",
    "意甲": r"^(意甲|意大利甲级联赛|Serie\s*A)$",
    "意乙": r"^(意乙|意大利乙级联赛|Serie\s*B)$",
    "德甲": r"^(德甲|德国甲级联赛|Bundesliga)$",
    "德乙": r"^(德乙|德国乙级联赛|2\.?Bundesliga)$",
    "法甲": r"^(法甲|法国甲级联赛|Ligue\s*1)$",
    "法乙": r"^(法乙|法国乙级联赛|Ligue\s*2)$"
}

# 联赛所属地区
LEAGUE_REGIONS = {
    "欧洲": ["英超", "英冠", "英甲", "英乙", "西甲", "西乙", "意甲", "意乙", "德甲", "德乙",
           "法甲", "法乙", "葡超", "葡甲", "荷甲", "荷乙", "苏超", "苏冠", "苏甲", "苏乙",
           "瑞士超", "瑞士甲", "瑞典超", "瑞甲", "瑞典乙", "瑞典丙", "挪超", "挪甲",
           "丹麦超", "丹麦甲", "丹麦乙", "丹麦丙", "俄超", "俄甲", "白俄超",
           "土耳其超", "爱尔兰超", "爱尔兰甲", "奥威超", "芬超"],
    "南美": ["阿根廷甲", "阿根廷乙", "智利甲", "智利乙", "巴西甲", "巴西乙"],
    "亚洲": ["中超", "日职联", "日职乙", "日职丙", "日职女甲", "日职女乙", "韩K", "韩K2", 
           "阿联酋超", "阿联酋甲", "乌兹超", "印度超", "印度甲", "越南联", "越南甲", 
           "马来超", "马来甲", "巴林超"],
    "大洋洲": ["澳超", "澳南超", "澳维甲", "澳维超", "澳昆超", "澳西超", "澳布超", "澳威超"],
    "北美": ["美职业", "美甲"]
}

# 联赛层级关系
LEAGUE_HIERARCHY = {
    "英格兰": {
        "level_1": "英超",
        "level_2": "英冠",
        "level_3": "英甲",
        "level_4": "英乙"
    },
    "西班牙": {
        "level_1": "西甲",
        "level_2": "西乙"
    },
    "意大利": {
        "level_1": "意甲",
        "level_2": "意乙"
    },
    "德国": {
        "level_1": "德甲",
        "level_2": "德乙"
    },
    "法国": {
        "level_1": "法甲",
        "level_2": "法乙"
    },
    "葡萄牙": {
        "level_1": "葡超",
        "level_2": "葡甲"
    },
    "荷兰": {
        "level_1": "荷甲",
        "level_2": "荷乙"
    },
    "苏格兰": {
        "level_1": "苏超",
        "level_2": "苏冠",
        "level_3": "苏甲",
        "level_4": "苏乙"
    },
    "瑞士": {
        "level_1": "瑞士超",
        "level_2": "瑞士甲"
    },
    "瑞典": {
        "level_1": "瑞典超",
        "level_2": "瑞甲"
    },
    "挪威": {
        "level_1": "挪超",
        "level_2": "挪甲"
    },
    "俄罗斯": {
        "level_1": "俄超",
        "level_2": "俄甲"
    },
    "爱尔兰": {
        "level_1": "爱尔兰超",
        "level_2": "爱尔兰甲"
    },
    "阿根廷": {
        "level_1": "阿根廷甲",
        "level_2": "阿根廷乙"
    },
    "智利": {
        "level_1": "智利甲",
        "level_2": "智利乙"
    },
    "巴西": {
        "level_1": "巴西甲",
        "level_2": "巴西乙"
    },
    "日本": {
        "level_1": "日职联",
        "level_2": "日职乙"
    },
    "韩国": {
        "level_1": "韩K",
        "level_2": "韩K2"
    },
    "阿联酋": {
        "level_1": "阿联酋超",
        "level_2": "阿联酋甲"
    },
    "芬兰": {
        "level_1": "芬超"
    }
}

def normalize_league_name(league_name):
    """
    标准化联赛名称，将各种变体映射到标准名称
    
    Args:
        league_name: 原始联赛名称
        
    Returns:
        str: 标准化后的联赛名称
    """
    if not league_name:
        return league_name
    
    # 清理名称
    clean_name = league_name.strip()
    
    # 直接匹配标准名称
    for standard_name, aliases in LEAGUE_STANDARD_NAMES.items():
        if clean_name == standard_name:
            return standard_name
        
        # 检查别名
        for alias in aliases:
            if clean_name == alias:
                return standard_name
    
    # 部分匹配 - 特别处理俄超
    if any(keyword in clean_name for keyword in ["俄罗斯", "Russian", "俄超"]):
        if any(level in clean_name for level in ["超", "超级", "Premier", "顶级"]):
            return "俄超"
        elif any(level in clean_name for level in ["甲", "甲级", "First"]):
            return "俄甲"
    
    # 如果没有找到匹配，返回原名称
    return clean_name