import tkinter as tk
from tkinter import ttk, scrolledtext
import logging
import traceback
from football_analysis_system.config import COLOR_BG, COLOR_HOME, COLOR_AWAY, COLOR_PRIMARY, COLOR_ACCENT, COLOR_TEXT, COLOR_TEXT_LIGHT
from football_analysis_system.config import FONT_LARGE, FONT_NORMAL, FONT_SUBHEADER

class FundamentalAnalysisUI:
    """基本面分析标签页现代化UI组件"""

    def __init__(self, parent):
        """
        初始化UI组件
        
        Args:
            parent: 父容器
        """
        self.parent = parent
        self.help_window = None  # 帮助窗口引用
        
        # UI控件引用
        self.match_info_frame = None
        self.match_label = None
        self.match_details_label = None
        
        self.control_frame = None
        self.analyze_button = None
        self.stop_button = None
        self.help_button = None
        
        self.status_frame = None
        self.status_label = None
        self.progress_var = None
        self.progress_bar = None
        
        self.results_frame = None
        self.results_notebook = None
        self.result_text = None
        self.no_results_label = None
        
        # 内部状态
        self.league_name = ""
        self.home_team = ""
        self.away_team = ""
        
    def create_widgets(self):
        """创建现代化标签页控件"""
        # 主布局使用网格布局，使结果区域占据大部分空间
        self.parent.grid_rowconfigure(3, weight=1)  # 结果区域占据剩余空间
        self.parent.grid_columnconfigure(0, weight=1)
        
        # 创建各个部分
        self.create_match_info_section()
        self.create_control_section()
        self.create_status_section()
        self.create_results_section()

    def create_match_info_section(self):
        """创建比赛信息区域"""
        self.match_info_frame = ttk.LabelFrame(self.parent, text="🏆 比赛信息")
        self.match_info_frame.grid(row=0, column=0, sticky="ew", padx=15, pady=(15, 5))

        # 比赛显示标签
        self.match_label = ttk.Label(self.match_info_frame, 
                                    text="未选择比赛", 
                                    font=FONT_LARGE,
                                    foreground=COLOR_PRIMARY)
        self.match_label.pack(padx=15, pady=15)

        # 比赛详情
        self.match_details_label = ttk.Label(self.match_info_frame, 
                                           text="请在左侧比赛列表中选择一场比赛", 
                                           font=FONT_NORMAL,
                                           foreground=COLOR_TEXT)
        self.match_details_label.pack(padx=15, pady=(0, 15))

    def create_control_section(self):
        """创建控制区域"""
        self.control_frame = ttk.LabelFrame(self.parent, text="🔬 分析控制")
        self.control_frame.grid(row=1, column=0, sticky="ew", padx=15, pady=5)

        # 按钮区域
        buttons_frame = tk.Frame(self.control_frame, bg=COLOR_BG)
        buttons_frame.pack(fill=tk.X, padx=10, pady=15)

        # 分析按钮
        self.analyze_button = ttk.Button(buttons_frame, 
                                        text="🚀 开始基本面分析",
                                        style="Accent.TButton",
                                        state="disabled")
        self.analyze_button.pack(side=tk.LEFT, padx=(0, 10))

        # 停止按钮
        self.stop_button = ttk.Button(buttons_frame,
                                     text="🛑 停止分析", 
                                     state="disabled")
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # 帮助按钮
        self.help_button = ttk.Button(buttons_frame, 
                                     text="❓ 信心位置说明",
                                     command=self.show_help,
                                     style="Primary.TButton")
        self.help_button.pack(side=tk.LEFT)

        # 说明文本
        help_text = ("💡 基本面分析说明：\n"
                    "• 自动获取实时比赛数据和统计信息\n"
                    "• 分析球队近期状态、实力对比\n"
                    "• 提供详细的信心位置和盘口分析\n"
                    "• 点击帮助按钮查看信心位置参考表")
        
        help_label = ttk.Label(self.control_frame, 
                              text=help_text,
                              font=("Arial", 9),
                              foreground=COLOR_TEXT,
                              justify=tk.LEFT)
        help_label.pack(padx=10, pady=(0, 10), anchor="w")

    def create_status_section(self):
        """创建状态显示区域"""
        self.status_frame = ttk.LabelFrame(self.parent, text="📊 分析状态")
        self.status_frame.grid(row=2, column=0, sticky="ew", padx=15, pady=5)

        # 进度条
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(self.status_frame, 
                                           variable=self.progress_var,
                                           mode='indeterminate')
        self.progress_bar.pack(fill=tk.X, padx=10, pady=10)
        self.progress_bar.pack_forget()  # 初始隐藏

        # 状态标签
        self.status_label = ttk.Label(self.status_frame, 
                                     text="准备就绪",
                                     font=FONT_NORMAL)
        self.status_label.pack(padx=10, pady=10)

    def create_results_section(self):
        """创建结果显示区域"""
        self.results_frame = ttk.LabelFrame(self.parent, text="🎯 分析结果")
        self.results_frame.grid(row=3, column=0, sticky="nsew", padx=15, pady=(5, 15))

        # 初始提示
        self.no_results_label = ttk.Label(self.results_frame, 
                                         text="暂无分析结果\n请选择比赛并开始基本面分析",
                                         font=FONT_NORMAL,
                                         foreground=COLOR_TEXT,
                                         justify=tk.CENTER)
        self.no_results_label.pack(expand=True, pady=50)

    def hide_control_panels(self):
        """分析开始后隐藏控制和状态区域，让结果区域占据更多空间"""
        # 先隐藏控制和状态区域
        self.match_info_frame.grid_remove()
        self.control_frame.grid_remove()
        self.status_frame.grid_remove()
        
        # 如果已有结果框架，调整它的位置
        if hasattr(self, 'results_frame') and self.results_frame:
            self.results_frame.grid(row=0, column=0, sticky="nsew", padx=15, pady=15)
    
    def show_control_panels(self):
        """恢复显示控制和状态区域"""
        self.match_info_frame.grid()
        self.control_frame.grid()
        self.status_frame.grid()
        
        # 恢复结果框架的位置
        if hasattr(self, 'results_frame') and self.results_frame:
            self.results_frame.grid(row=3, column=0, sticky="nsew", padx=15, pady=(5, 15))

    def create_results_display(self):
        """创建结果显示区域（当有结果时调用）"""
        try:
            logging.info("开始创建结果显示区域...")
            
            # 清空现有内容
            for widget in self.results_frame.winfo_children():
                logging.debug(f"销毁现有控件: {widget}")
                widget.destroy()

            # 隐藏控制面板，让结果区域占据更多空间
            self.hide_control_panels()

            # 创建结果标签页
            self.results_notebook = ttk.Notebook(self.results_frame)
            self.results_notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 创建详细分析标签页
            analysis_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(analysis_frame, text="📋 详细分析")

            # 创建分析结果文本区域
            self.result_text = scrolledtext.ScrolledText(analysis_frame,
                                                       wrap=tk.WORD,
                                                       font=("Consolas", 10),
                                                       bg=COLOR_BG,
                                                       fg=COLOR_TEXT)
            self.result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            self.result_text.config(state=tk.DISABLED)  # 只读模式

            # 创建摘要标签页
            summary_frame = tk.Frame(self.results_notebook, bg=COLOR_BG)
            self.results_notebook.add(summary_frame, text="📊 分析摘要")

            # 摘要内容将在append_result时填充
            self.summary_text = scrolledtext.ScrolledText(summary_frame,
                                                         wrap=tk.WORD,
                                                         font=("Arial", 11),
                                                         bg=COLOR_BG,
                                                         fg=COLOR_TEXT)
            self.summary_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            self.summary_text.config(state=tk.DISABLED)
            
            # 选择详细分析标签页
            self.results_notebook.select(0)
            
            logging.info("结果显示区域创建完成")
            
        except Exception as e:
            logging.error(f"创建结果显示区域失败: {e}")
            logging.error(traceback.format_exc())
    
    def update_match_info(self, league_name, home_team, away_team, preserve_results=False):
        """
        更新比赛信息显示

        Args:
            league_name: 联赛名称
            home_team: 主队名称
            away_team: 客队名称
            preserve_results: 是否保持现有的分析结果，默认为False
        """
        self.league_name = league_name
        self.home_team = home_team
        self.away_team = away_team
        
        if home_team and away_team:
            # 更新比赛标题
            match_title = f"{home_team} vs {away_team}"
            self.match_label.config(text=match_title)

            # 更新比赛详情
            details = []
            if league_name:
                details.append(f"🏆 联赛: {league_name}")
            details.append("📊 准备进行基本面分析")

            self.match_details_label.config(text=" | ".join(details))
            
            # 启用分析按钮
            self.analyze_button.config(state="normal")

            # 只有在不保持结果时才清空之前的结果
            if not preserve_results:
                self.clear_result()
        else:
            self.match_label.config(text="未选择比赛")
            self.match_details_label.config(text="请在左侧比赛列表中选择一场比赛")
            self.analyze_button.config(state="disabled")

    def show_status(self, text, is_error=False):
        """
        显示状态信息
        
        Args:
            text: 状态文本
            is_error: 是否为错误状态
        """
        if is_error:
            self.status_label.config(text=f"❌ {text}", foreground="red")
        else:
            self.status_label.config(text=f"ℹ️ {text}", foreground=COLOR_TEXT)
    
    def start_progress(self):
        """开始进度条动画"""
        self.progress_bar.pack(fill=tk.X, padx=10, pady=10)
        self.progress_bar.start(10)
    
    def stop_progress(self):
        """停止进度条动画"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
    
    def enable_analyze_button(self):
        """启用分析按钮"""
        self.analyze_button.config(state=tk.NORMAL)
    
    def disable_analyze_button(self):
        """禁用分析按钮"""
        self.analyze_button.config(state=tk.DISABLED)
    
    def add_stop_button(self, command):
        """
        添加停止按钮命令
        
        Args:
            command: 按钮点击事件处理函数
        """
        self.stop_button.config(command=command)
        
    def enable_stop_button(self):
        """启用停止按钮"""
        self.stop_button.config(state=tk.NORMAL)
        
    def disable_stop_button(self):
        """禁用停止按钮"""
        self.stop_button.config(state=tk.DISABLED)
    
    def append_result(self, text):
        """
        向结果文本框追加内容
        
        Args:
            text: 要追加的文本
        """
        try:
            # 如果还没有创建结果显示区域，则创建
            if not hasattr(self, 'result_text') or self.result_text is None:
                logging.info("创建结果显示区域 - result_text不存在或为None...")
                self.create_results_display()
            elif not self.result_text.winfo_exists():
                logging.info("创建结果显示区域 - result_text已销毁...")
                self.create_results_display()

            # 再次检查是否创建成功
            if self.result_text is None:
                logging.error("结果文本框创建失败，无法追加内容")
                return

            logging.debug(f"追加文本到结果区域: {text[:50]}...")
            
            self.result_text.config(state=tk.NORMAL)
            self.result_text.insert(tk.END, text)
            # 滚动到顶部而不是底部
            self.result_text.see("1.0")
            self.result_text.config(state=tk.DISABLED)
            
            # 同时更新摘要（提取关键信息）
            self.update_summary(text)
            
        except Exception as e:
            logging.error(f"追加结果文本失败: {e}")
            logging.error(traceback.format_exc())
    
    def append_colored_result(self, text, color=None, tag=None):
        """
        向结果文本框追加带颜色的内容
        
        Args:
            text: 要追加的文本
            color: 文本颜色
            tag: 标签名称（如果为None则自动生成）
        """
        try:
            # 如果还没有创建结果显示区域，则创建
            if not hasattr(self, 'result_text') or self.result_text is None:
                logging.info("创建结果显示区域 - result_text不存在或为None...")
                self.create_results_display()
            elif not self.result_text.winfo_exists():
                logging.info("创建结果显示区域 - result_text已销毁...")
                self.create_results_display()

            # 再次检查是否创建成功
            if self.result_text is None:
                logging.error("结果文本框创建失败，无法追加彩色内容")
                return

            logging.debug(f"追加彩色文本到结果区域: {text[:50]}...")
            
            self.result_text.config(state=tk.NORMAL)

            if tag is None and color is not None:
                # 为每次插入的不同颜色文本创建唯一标签
                tag = f"color_{len(self.result_text.tag_names())}"
                self.result_text.tag_configure(tag, foreground=color)

            if tag:
                self.result_text.insert(tk.END, text, tag)
            else:
                self.result_text.insert(tk.END, text)

            # 滚动到顶部而不是底部
            self.result_text.see("1.0")
            self.result_text.config(state=tk.DISABLED)
            
            # 同时更新摘要
            self.update_summary(text)
            
        except Exception as e:
            logging.error(f"追加彩色结果文本失败: {e}")
            logging.error(traceback.format_exc())

    def update_summary(self, text):
        """更新分析摘要"""
        try:
            if not hasattr(self, 'summary_text') or self.summary_text is None:
                return
            elif not self.summary_text.winfo_exists():
                return

            # 提取关键信息用于摘要
            if any(keyword in text for keyword in ['信心', '推荐', '评分', '建议', '结论']):
                self.summary_text.config(state=tk.NORMAL)
                self.summary_text.insert(tk.END, text)
                # 滚动到顶部而不是底部
                self.summary_text.see("1.0")
                self.summary_text.config(state=tk.DISABLED)
        except Exception as e:
            logging.error(f"更新摘要失败: {e}")
            # 不需要打印完整堆栈，因为这不是关键错误
    
    def clear_result(self):
        """清空分析结果"""
        try:
            # 恢复初始状态
            for widget in self.results_frame.winfo_children():
                widget.destroy()

            # 重置变量
            self.result_text = None
            self.summary_text = None

            self.no_results_label = ttk.Label(self.results_frame, 
                                             text="暂无分析结果\n请选择比赛并开始基本面分析",
                                             font=FONT_NORMAL,
                                             foreground=COLOR_TEXT,
                                             justify=tk.CENTER)
            self.no_results_label.pack(expand=True, pady=50)
            
            logging.info("分析结果已清空")
            
        except Exception as e:
            logging.error(f"清空分析结果失败: {e}")
            logging.error(traceback.format_exc())

    def show_help(self):
        """显示信心位置帮助信息"""
        # 检查是否已有帮助窗口打开
        if self.help_window is not None and self.help_window.winfo_exists():
            # 如果已经存在窗口，将其提到前台
            self.help_window.lift()
            self.help_window.focus_set()
            return
            
        # 创建一个新的窗口
        self.help_window = tk.Toplevel(self.parent)
        self.help_window.title("📚 信心位置参考说明")
        self.help_window.geometry("950x850")
        self.help_window.configure(bg=COLOR_BG)
        
        # 设置窗口关闭时的回调函数，清除窗口引用
        self.help_window.protocol("WM_DELETE_WINDOW", self._on_help_window_close)
        
        # 创建说明标签
        title_label = tk.Label(self.help_window, 
                              text="📊 信心位置能够打出的最佳位置参考表",
                              font=FONT_LARGE,
                              bg=COLOR_BG, 
                              fg=COLOR_PRIMARY)
        title_label.pack(pady=(15, 10))
        
        # 创建主滚动窗口
        main_frame = ttk.Frame(self.help_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)
        
        canvas = tk.Canvas(main_frame, bg=COLOR_BG)
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=canvas.yview)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        content_frame = ttk.Frame(canvas)
        canvas.create_window((0, 0), window=content_frame, anchor="nw")
        
        # 定义信心区间
        confidence_levels = [
            {"name": "极致信心", "range": "(9-10)", "emoji": "🔥", "color": "#FF4444", "values": [
                "1.超实盘低水", "2.超实盘中低水", "3.超实盘超低水", "4.超深盘中上水", "5.超散盘中高水", "6.超深盘中下水"
            ]},
            {"name": "信心充足", "range": "(7.9-8.9)", "emoji": "💪", "color": "#FF8800", "values": [
                "1.超实盘高水", "2.超实盘中高水", "3.超实盘中水", "4.超实盘中低水", "5.超实盘低水", "6.超实盘超低水"
            ]},
            {"name": "信心中上", "range": "(6.8-7.8)", "emoji": "👍", "color": "#44AA44", "values": [
                "1.超实盘高水", "2.超实盘中高水", "3.实开盘超低水", "4.实开盘低水", "5.实开盘中低水", 
                "6.超实盘超高水", "7.超实盘中水", "8.超实盘中低水"
            ]},
            {"name": "信心中庸", "range": "(4.2-6.7)", "emoji": "🤔", "color": "#4488CC", "values": [
                "1.实开盘中低水", "2.实开盘低水", "3.实开盘超高水", "4.实开盘高水", 
                "5.超实盘高水", "6.韬光盘低水", "7.超实盘中高水", "8.韬光盘超低水", "9.韬光盘中高水"
            ]},
            {"name": "信心中下", "range": "(2.8-4.1)", "emoji": "😐", "color": "#AA8844", "values": [
                "1.韬光盘低水", "2.韬光盘中低水", "3.实开盘超高水", "4.实开盘高水", 
                "5.实开盘中高水", "6.实开盘中水", "7.实开盘中低水", "8.实开盘低水"
            ]},
            {"name": "信心不足", "range": "(1.4-2.7)", "emoji": "😕", "color": "#CC6644", "values": [
                "1.韬光盘低水", "2.韬光盘中低水", "3.实开盘低水", "4.实开盘中低水", "5.实开盘中水"
            ]},
            {"name": "欠缺信心", "range": "(0-1.3)", "emoji": "😰", "color": "#BB4444", "values": [
                "1.实开盘高水", "2.实开盘中高水", "3.实开盘中水", "4.实开盘中低水", "5.实开盘低水"
            ]}
        ]
        
        # 为每个信心区间创建现代化卡片
        for i, level in enumerate(confidence_levels):
            # 创建卡片容器
            card_frame = tk.Frame(content_frame, bg="#F8F9FA", relief="raised", bd=1)
            card_frame.pack(fill="x", padx=10, pady=8)
            
            # 创建卡片标题
            title_frame = tk.Frame(card_frame, bg=level["color"])
            title_frame.pack(fill="x", padx=2, pady=2)
            
            title_label = tk.Label(
                title_frame,
                text=f"{level['emoji']} {level['name']} {level['range']}",
                font=("Arial", 13, "bold"),
                bg=level["color"],
                fg="white",
                pady=8
            )
            title_label.pack()
            
            # 创建内容区域
            content_area = tk.Frame(card_frame, bg="#FFFFFF")
            content_area.pack(fill="x", padx=2, pady=(0, 2))
            
            # 显示位置列表
            positions_text = " • ".join(level["values"])
            positions_label = tk.Label(
                content_area,
                text=positions_text,
                font=("Arial", 10),
                bg="#FFFFFF",
                fg="#333333",
                wraplength=800,
                justify="left",
                pady=10,
                padx=15
            )
            positions_label.pack(fill="x")
        
        # 更新canvas滚动区域
        def update_scrollregion(event):
            canvas.configure(scrollregion=canvas.bbox("all"))
        
        content_frame.bind("<Configure>", update_scrollregion)
        
        # 添加重要说明卡片
        note_frame = tk.Frame(self.help_window, bg="#E8F4FD", relief="solid", bd=1)
        note_frame.pack(fill=tk.X, padx=15, pady=10)
        
        note_title = tk.Label(note_frame, 
                             text="💡 重要说明",
                             font=("Arial", 12, "bold"),
                             bg="#E8F4FD", 
                             fg=COLOR_PRIMARY)
        note_title.pack(pady=(10, 5))
        
        note_text = """• 信心充足时初始盘口低开代表认可，即时盘维持低开则代表否认
• 信心不足时盘口高开代表认可，维持高开则代表否认
• 韬光盘多出现在信心不足和信心中庸阶段
• 超实盘多出现在信心充足和极致信心阶段
• 注意观察盘口与水位的协同变化，两者结合更准确"""
        
        note_label = tk.Label(note_frame, 
                             text=note_text,
                             font=("Arial", 10),
                             bg="#E8F4FD", 
                             fg="#2C3E50",
                             justify="left",
                             wraplength=900)
        note_label.pack(padx=15, pady=(0, 15))
        
        # 添加关闭按钮
        close_button = ttk.Button(self.help_window, 
                                 text="✅ 关闭",
                                 command=self._on_help_window_close,
                                 style="Accent.TButton")
        close_button.pack(pady=15)
        
        # 确保窗口在前台显示
        self.help_window.transient(self.parent)
        self.help_window.grab_set()

    def _on_help_window_close(self):
        """处理帮助窗口关闭事件"""
        if self.help_window:
            self.help_window.destroy()
            self.help_window = None 