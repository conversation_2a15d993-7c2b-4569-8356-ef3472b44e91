"""
统一配置管理器
整合所有配置信息，提供统一的配置接口
"""

import os
import json
import configparser
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """统一配置管理器"""
    
    _instance = None
    _config_loaded = False
    
    def __new__(cls):
        """单例模式"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._config_loaded:
            # 动态计算项目根目录
            self.project_root = self._get_project_root()
            self.config_dir = self.project_root / "config"
            self.config_dir.mkdir(exist_ok=True)

            # 默认配置
            self._config = {
                "app": {
                    "title": "足球比赛分析系统 Plus",
                    "version": "v4.0 现代版",
                    "debug": False
                },
                "paths": {
                    "project_root": str(self.project_root),
                    "data_dir": str(self.project_root / "data"),
                    "logs_dir": str(self.project_root / "logs"),
                    "temp_dir": str(self.project_root / "temp"),
                    "backup_dir": str(self.project_root / "backup")
                },
                "database": {
                    "matches": "matches_and_odds.db",
                    "teams": "team_database.db",
                    "odds_intervals": "odds_database.db",
                    "strength_matchup": "sports_database.db",
                    "william_hill_team": "WilliamHillTeam.db",
                    "standard_odds": "StandardOdds.db",
                    "guangyishili": "guangyishili.db"
                },
                "logging": {
                    "level": "INFO",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "console_output": True,
                    "file_output": True,
                    "max_file_size": 10 * 1024 * 1024,  # 10MB
                    "backup_count": 5,
                    "rotate_daily": True
                },
                "crawler": {
                    "max_workers": 10,
                    "request_timeout": 30,
                    "retry_times": 3,
                    "min_delay": 0.5,
                    "max_delay": 2.0,
                    "batch_size": 50,
                    "requests_per_second": 2.0,
                    "circuit_breaker_threshold": 25
                },
                "ui": {
                    "theme": "modern",
                    "font_family": "Segoe UI",
                    "window_size": "1400x900",
                    "colors": {
                        "primary": "#1a237e",
                        "secondary": "#3949ab",
                        "accent": "#00c853",
                        "background": "#f5f5f7",
                        "text": "#212121",
                        "success": "#27ae60",
                        "warning": "#f44336",
                        "info": "#2196f3"
                    }
                },
                "api": {
                    "deepseek": {
                        "api_key": "",  # 从环境变量获取
                        "api_url": "https://api.deepseek.com/v1/chat/completions",
                        "model": "deepseek-reasoner"
                    }
                },
                "companies": {
                    "281": {"en": "Bet 365", "cn": "365"},
                    "115": {"en": "William Hill", "cn": "威廉希尔"},
                    "90": {"en": "Easybets", "cn": "易胜博"},
                    "177": {"en": "Pinnacle", "cn": "平博"},
                    "82": {"en": "Ladbrokes", "cn": "立博"},
                    "432": {"en": "HKJC", "cn": "香港马会"},
                    "80": {"en": "Macau", "cn": "澳门"}
                }
            }
            
            self._load_config()
            self._ensure_directories()
            self._config_loaded = True

    def _get_project_root(self) -> Path:
        """
        动态获取项目根目录
        支持不同的部署环境和目录结构
        """
        # 方法1: 基于当前文件位置计算
        current_file = Path(__file__).resolve()

        # core/config_manager.py -> core -> football_analysis_system
        potential_root = current_file.parent.parent

        # 验证这是否是正确的项目根目录
        if self._is_valid_project_root(potential_root):
            return potential_root

        # 方法2: 向上查找包含特定标识文件的目录
        search_path = current_file.parent
        for _ in range(5):  # 最多向上查找5级目录
            if self._is_valid_project_root(search_path):
                return search_path
            search_path = search_path.parent
            if search_path == search_path.parent:  # 到达根目录
                break

        # 方法3: 使用环境变量
        env_root = os.environ.get('FOOTBALL_PROJECT_ROOT')
        if env_root and Path(env_root).exists():
            env_path = Path(env_root)
            if self._is_valid_project_root(env_path):
                return env_path

        # 方法4: 使用当前工作目录
        cwd = Path.cwd()
        if self._is_valid_project_root(cwd):
            return cwd

        # 如果都失败了，回退到基于文件位置的计算
        print(f"警告: 无法确定项目根目录，使用默认位置: {potential_root}")
        return potential_root

    def _is_valid_project_root(self, path: Path) -> bool:
        """
        检查路径是否是有效的项目根目录
        """
        if not path.exists():
            return False

        # 检查是否包含项目特征文件/目录
        indicators = [
            'main.py',
            'config.py',
            'core',
            'ui',
            'analysis',
            'scrapers'
        ]

        found_indicators = 0
        for indicator in indicators:
            if (path / indicator).exists():
                found_indicators += 1

        # 至少要有一半的指示器存在
        return found_indicators >= len(indicators) // 2
    
    def _load_config(self):
        """加载配置文件"""
        # 保存动态计算的路径配置，防止被硬编码路径覆盖
        dynamic_paths = self._config['paths'].copy()

        # 1. 加载主配置文件 (JSON格式)
        main_config_file = self.config_dir / "config.json"
        if main_config_file.exists():
            try:
                with open(main_config_file, 'r', encoding='utf-8') as f:
                    user_config = json.load(f)
                    # 临时移除路径配置，避免硬编码路径覆盖动态路径
                    user_config.pop('paths', {})  # 移除用户配置中的路径设置
                    self._deep_update(self._config, user_config)

                    # 总是使用动态计算的路径，确保可移植性
                    self._config['paths'] = dynamic_paths
                    print(f"使用动态路径配置，项目根目录: {dynamic_paths['project_root']}")

                    # 如果用户配置中有特殊的路径覆盖需求，可以通过环境变量实现
                    env_data_dir = os.environ.get('FOOTBALL_DATA_DIR')
                    if env_data_dir:
                        self._config['paths']['data_dir'] = env_data_dir
                        print(f"使用环境变量指定的数据目录: {env_data_dir}")

            except Exception as e:
                print(f"加载主配置文件失败: {e}")

        # 2. 兼容旧的INI配置文件
        self._load_legacy_ini_config()

        # 3. 从环境变量加载敏感配置
        self._load_env_config()

        # 4. 保存当前配置到文件
        self.save_config()
    
    def _load_legacy_ini_config(self):
        """加载旧版INI配置文件以保持兼容性"""
        # 检查根目录和项目目录的INI文件
        ini_files = [
            self.project_root.parent / "config.ini",
            self.project_root / "config.ini"
        ]
        
        for ini_file in ini_files:
            if ini_file.exists():
                try:
                    config = configparser.ConfigParser()
                    config.read(ini_file, encoding='utf-8')
                    
                    # 转换INI配置到新格式
                    if 'CRAWLER' in config:
                        crawler_section = config['CRAWLER']
                        self._config['crawler'].update({
                            'max_workers': int(crawler_section.get('max_workers', 10)),
                            'request_timeout': int(crawler_section.get('request_timeout', 30)),
                            'retry_times': int(crawler_section.get('retry_times', 3)),
                            'min_delay': float(crawler_section.get('min_delay', 0.5)),
                            'max_delay': float(crawler_section.get('max_delay', 2.0)),
                            'batch_size': int(crawler_section.get('batch_size', 50))
                        })
                    
                    if 'TARGET_COMPANIES' in config:
                        companies_str = config['TARGET_COMPANIES'].get('companies', '{}')
                        try:
                            companies = json.loads(companies_str)
                            self._config['companies'].update(companies)
                        except json.JSONDecodeError:
                            pass
                    
                except Exception as e:
                    print(f"加载INI配置文件 {ini_file} 失败: {e}")
    
    def _load_env_config(self):
        """从环境变量加载配置"""
        # API密钥
        deepseek_key = os.environ.get('DEEPSEEK_API_KEY')
        if deepseek_key:
            self._config['api']['deepseek']['api_key'] = deepseek_key
        
        # 数据库路径
        db_path = os.environ.get('FOOTBALL_DB_PATH')
        if db_path:
            self._config['paths']['data_dir'] = db_path
        
        # 日志级别
        log_level = os.environ.get('LOG_LEVEL')
        if log_level:
            self._config['logging']['level'] = log_level.upper()
    
    def _deep_update(self, base_dict: Dict, update_dict: Dict):
        """深度更新字典"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        for dir_key in ['data_dir', 'logs_dir', 'temp_dir', 'backup_dir']:
            dir_path = Path(self._config['paths'][dir_key])
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def get(self, key_path: str, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path: 配置键路径，使用点分隔，如 'database.matches'
            default: 默认值
            
        Returns:
            配置值
        """
        keys = key_path.split('.')
        value = self._config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """
        设置配置值
        
        Args:
            key_path: 配置键路径
            value: 配置值
        """
        keys = key_path.split('.')
        config = self._config
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        config[keys[-1]] = value
    
    def get_database_path(self, db_name: str) -> str:
        """获取数据库完整路径"""
        data_dir = Path(self.get('paths.data_dir'))
        db_file = self.get(f'database.{db_name}', f'{db_name}.db')
        return str(data_dir / db_file)
    
    def resolve_database_path(self, path: str) -> str:
        """
        解析数据库路径，将相对路径转换为标准路径
        
        Args:
            path: 原始路径
            
        Returns:
            str: 解析后的标准路径
        """
        # 导入PathResolver（避免循环导入）
        from .path_resolver import PathResolver
        return PathResolver.resolve_relative_path(path)
    
    def get_log_file_path(self, log_name: str = 'app') -> str:
        """获取日志文件路径"""
        logs_dir = Path(self.get('paths.logs_dir'))
        timestamp = self._get_date_string()
        return str(logs_dir / f"{log_name}_{timestamp}.log")
    
    def get_temp_file_path(self, filename: str) -> str:
        """获取临时文件路径"""
        temp_dir = Path(self.get('paths.temp_dir'))
        return str(temp_dir / filename)
    
    def _get_date_string(self) -> str:
        """获取日期字符串"""
        from datetime import datetime
        return datetime.now().strftime('%Y%m%d')
    
    def save_config(self):
        """保存配置到文件"""
        config_file = self.config_dir / "config.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存配置文件失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._config_loaded = False
        self.__init__()
    
    def get_all_config(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def export_config(self, file_path: str):
        """导出配置到指定文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(self._config, f, ensure_ascii=False, indent=2)


# 全局配置管理器实例
config_manager = ConfigManager() 